// bus, created by TheDanishMaster in 02/11/13 21:57:12, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 0 30"
		"MassOverride"			"10000"
		"AddGravity"			"0.5"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"750"
		"MaxRPM"				"2500"
		"MaxSpeed"				"60"
		"MaxReverseSpeed"		"20"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"1"
		"AxleRatio"				"6"
		"Gear"					"3.2"
		"Gear"					"2.4"
		"Gear"					"1.5"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"2200"
		"ShiftDownRPM"			"1100"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"18"
		"DegreesBoost"						"11"
		"FastDampen"						"0"
		"SteeringExponent"					"1.4"
		"SlowCarSpeed"						"20"
		"FastCarSpeed"						"35"
		"SlowSteeringRate"					"4"
		"FastSteeringRate"					"2"
		"SteeringRestRateSlow"				"4"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"1"
		"BrakeSteeringRateFactor"			"5"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"27"
			"Mass"							"200"
			"Inertia"						"0"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"60"
			"SpringDamping"					"0.39"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"3.11"
			"MaxBodyForce"					"11"
		}
		"TorqueFactor"						"0.3"
		"BrakeFactor"						"0.6"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"27"
			"Mass"							"300"
			"Inertia"						"0"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"90"
			"SpringDamping"					"0.39"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"3.11"
			"MaxBodyForce"					"11"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.6"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.88"
		"Speed_Approach_Factor"	"0.108"
	}
	"Gear"
	{
		"Max_Speed"				"0.98"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1.16"
		"Speed_Approach_Factor"	"0.015"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"vehicles\tdmcars\bus\engine_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\tdmcars\bus\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"vehicles\tdmcars\bus\engine_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\tdmcars\bus\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\tdmcars\bus\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\tdmcars\bus\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\tdmcars\bus\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\tdmcars\bus\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\tdmcars\bus\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\tdmcars\bus\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\tdmcars\bus\engine_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\tdmcars\bus\engine_start.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\tdmcars\bus\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\tdmcars\bus\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\tdmcars\bus\idle.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\tdmcars\bus\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\tdmcars\bus\second.mp3"
		"Min_Time"	"1.25"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\tdmcars\bus\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"tdmtruck_skid_highfriction"
	"Skid_NormalFriction"	"tdmtruck_skid_highfriction"
	"Skid_HighFriction"		"tdmtruck_skid_highfriction"
}