// crsk_rolls-royce_silvercloud3, created by ᶜʳᵉᵉᵖᵉʳᵀᵛ in Fri Jan 26 20:47:22 2018, using Vehicle Controller (VCMod).

"Vehicle"
{
 "WheelsPerAxle" "2"
 "Body"
 {
 "CounterTorqueFactor" "0.9"
 "MassCenterOverride" "0 -5 10"
 "MassOverride" "1830"
 "AddGravity" "0.5"
 "MaxAngularVelocity" "100"
 }
 "Engine"
 {
 "HorsePower" "300"
 "MaxRPM" "3200"
 "MaxSpeed" "83"
 "MaxReverseSpeed" "22"
 "AutobrakeSpeedGain" "1.1"
 "AutobrakeSpeedFactor" "3"
 "Autotransmission" "0"
 "AxleRatio" "6.5"
 "Gear" "3.2"

 "ShiftUpRPM" "3800"
 "ShiftDownRPM" "1600"
 }
 "Steering"
 {
 "DegreesSlow" "40"
 "DegreesFast" "13"
 "DegreesBoost" "11"
 "FastDampen" "80"
 "SteeringExponent" "0"
 "SlowCarSpeed" "14"
 "FastCarSpeed" "60"
 "SlowSteeringRate" "3"
 "FastSteeringRate" "2"
 "SteeringRestRateSlow" "3"
 "SteeringRestRateFast" "2"
 "TurnThrottleReduceSlow" "0.01"
 "TurnThrottleReduceFast" "0.6"
 "BrakeSteeringRateFactor" "3"
 "ThrottleSteeringRestRateFactor" "2"
 "BoostSteeringRestRateFactor" "1.7"
 "BoostSteeringRateFactor" "1.7"

 "PowerSlideAccel" "250"

 "SkidAllowed" "1"
 "DustCloud" "1"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "16"
 "Mass" "200"
 "Inertia" "0.6"
 "Damping" "0"
 "RotDamping" "0"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "50"
 "SpringDamping" "0.1"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "5.5"
 "MaxBodyForce" "12"
 }
 "TorqueFactor" "0"
 "BrakeFactor" "0.4"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "16"
 "Mass" "250"
 "Inertia" "0.8"
 "Damping" "0.6"
 "RotDamping" "0.7"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "75"
 "SpringDamping" "0.1"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "5.5"
 "MaxBodyForce" "16"
 }
 "TorqueFactor" "0.6"
 "BrakeFactor" "0.6"
 }
}

"Vehicle_Sounds"
{
 "Gear"
 {
 "Max_Speed" "0.01"
 "Speed_Approach_Factor" "1"
 }
 "Gear"
 {
 "Max_Speed" "0.3"
 "Speed_Approach_Factor" "0.08"
 }
 "Gear"
 {
 "Max_Speed" "0.47"
 "Speed_Approach_Factor" "0.05"
 }
 "Gear"
 {
 "Max_Speed" "0.65"
 "Speed_Approach_Factor" "0.035"
 }
 "Gear"
 {
 "Max_Speed" "1"
 "Speed_Approach_Factor" "0.01"
 }
 "State"
 {
 "Name" "SS_SHUTDOWN_WATER"
 "Sound" "atv_stall_in_water"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_2_RESUME"
 "Sound" "vehicles\tdmcars\gullwing\third.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_START_WATER"
 "Sound" "atv_start_in_water"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_3_RESUME"
 "Sound" "vehicles\tdmcars\gullwing\fourth_cruise.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_REVERSE"
 "Sound" "vehicles\tdmcars\gullwing\rev.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_IDLE"
 "Sound" "vehicles\tdmcars\gullwing\idle.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_1_RESUME"
 "Sound" "vehicles\tdmcars\gullwing\first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_4"
 "Sound" "vehicles\tdmcars\gullwing\fourth_cruise.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_1"
 "Sound" "vehicles\tdmcars\gullwing\first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN_HIGHSPEED"
 "Sound" "vehicles\tdmcars\gullwing\throttle_off.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_SHUTDOWN"
 "Sound" "vehicles\tdmcars\slsamg\engineoff.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_START_IDLE"
 "Sound" "vehicles\tdmcars\gullwing\enginestart.mp3"
 "Min_Time" "3"
 }
 "State"
 {
 "Name" "SS_GEAR_4_RESUME"
 "Sound" "vehicles\tdmcars\gullwing\fourth_cruise.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_3"
 "Sound" "vehicles\tdmcars\gullwing\third.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_0_RESUME"
 "Sound" "vehicles\tdmcars\gullwing\first.mp3"
 "Min_Time" "0.75"
 }
 "State"
 {
 "Name" "SS_GEAR_0"
 "Sound" "vehicles\tdmcars\gullwing\first.mp3"
 "Min_Time" "0.08"
 }
 "State"
 {
 "Name" "SS_GEAR_2"
 "Sound" "vehicles\tdmcars\gullwing\second.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN"
 "Sound" "vehicles\tdmcars\gullwing\idle.mp3"
 "Min_Time" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "350"
 "Min_Speed_Change" "250"
 "Sound" "atv_impact_medium"
 "Gear_Limit" "1"
 }
 "CrashSound"
 {
 "Min_Speed" "450"
 "Min_Speed_Change" "350"
 "Sound" "atv_impact_heavy"
 "Gear_Limit" "0"
 }

 "Skid_LowFriction" ""
 "Skid_NormalFriction" ""
 "Skid_HighFriction" ""
}
