// pullman, created by [<PERSON>] <PERSON>n in Sat Jun 25 14:07:56 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1.009"
		"MassCenterOverride"	"0 0 3"
		"MassOverride"			"2266"
		"AddGravity"			"0.74"
		"MaxAngularVelocity"	"814"
	}
	"Engine"
	{
		"HorsePower"			"321"
		"MaxRPM"				"3500"
		"MaxSpeed"				"60"
		"MaxReverseSpeed"		"27"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"4"
		"Autotransmission"		"0"
		"AxleRatio"				"5.07"
		"Gear"					"3"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"10"
		"DegreesBoost"						"9"
		"FastDampen"						"79"
		"SteeringExponent"					"0.59"
		"SlowCarSpeed"						"4"
		"FastCarSpeed"						"61"
		"SlowSteeringRate"					"2.71"
		"FastSteeringRate"					"2"
		"SteeringRestRateSlow"				"2.7"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"2.7"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.458"
			"Mass"							"220"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"123"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"54"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.67"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.458"
			"Mass"							"220"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"131"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"54"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.67"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.06"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.25"
		"Speed_Approach_Factor"	"0.08"
	}
	"Gear"
	{
		"Max_Speed"				"0.35"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.55"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.01"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\rwcars\bmw_750_li\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\rwcars\bmw_750_li\fourth.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\rwcars\bmw_750_li\reverse.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\rwcars\bmw_750_li\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\rwcars\bmw_750_li\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\rwcars\bmw_750_li\fourth.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\rwcars\bmw_750_li\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\rwcars\bmw_750_li\throttle_slowdown.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"tdmcars_engine_off"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\rwcars\bmw_750_li\start.mp3"
		"Min_Time"	"0.46"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\rwcars\bmw_750_li\fourth.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\rwcars\bmw_750_li\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\rwcars\bmw_750_li\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\rwcars\bmw_750_li\idle.mp3"
		"Min_Time"	"0.08"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\rwcars\bmw_750_li\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\rwcars\bmw_750_li\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"490"
		"Min_Speed_Change"	"360"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}