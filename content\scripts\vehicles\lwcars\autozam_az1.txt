// autozam_az1, created by [FL] Conn in Fri Jun 24 16:37:50 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.99"
		"MassCenterOverride"	"0 -4 5"
		"MassOverride"			"800"
		"AddGravity"			"0.8"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"80"
		"MaxRPM"				"4000"
		"MaxSpeed"				"70"
		"MaxReverseSpeed"		"26"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"4.9"
		"Gear"					"4.0"
		"Gear"					"3.6"
		"Gear"					"3.1"
		"Gear"					"0.8"
		"Gear"					"0.4"

		"ShiftUpRPM"			"2600"
		"ShiftDownRPM"			"1100"
	}
	"Steering"
	{
		"DegreesSlow"						"44"
		"DegreesFast"						"20"
		"DegreesBoost"						"11"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"10"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"3"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"2.7"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.2"
		"BrakeSteeringRateFactor"			"2.7"
		"ThrottleSteeringRestRateFactor"	"1.67"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"11"
			"Mass"							"180"
			"Inertia"						"0"
			"Damping"						"0.2"
			"RotDamping"					"0.5"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"110"
			"SpringDamping"					"0.8"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"6"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.3"
		"BrakeFactor"						"0.65"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"11"
			"Mass"							"240"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0.8"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"110"
			"SpringDamping"					"0.8"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"1"
		"BrakeFactor"						"0.65"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.2"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.38"
		"Speed_Approach_Factor"	"0.052"
	}
	"Gear"
	{
		"Max_Speed"				"0.49"
		"Speed_Approach_Factor"	"0.034"
	}
	"Gear"
	{
		"Max_Speed"				"1.5"
		"Speed_Approach_Factor"	"0.033"
	}
	"Gear"
	{
		"Max_Speed"				"2"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/lwcars/autozam_az1/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/lwcars/autozam_az1/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/lwcars/autozam_az1/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/lwcars/autozam_az1/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/lwcars/autozam_az1/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/lwcars/autozam_az1/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/lwcars/autozam_az1/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/lwcars/autozam_az1/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/lwcars/shelby_glhs/startup.mp3"
		"Min_Time"	"4"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/lwcars/autozam_az1/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/lwcars/autozam_az1/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/lwcars/autozam_az1/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/lwcars/autozam_az1/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/lwcars/autozam_az1/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/lwcars/autozam_az1/slowdown_slow.mp3"
		"Min_Time"	"2"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
