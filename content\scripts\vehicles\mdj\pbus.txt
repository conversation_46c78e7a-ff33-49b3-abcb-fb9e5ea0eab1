// pbus, created by [<PERSON>] Conn in Fri Jun 24 16:50:45 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1"
		"MassCenterOverride"	"0 0 15"
		"MassOverride"			"10000"
		"AddGravity"			"0.6"
		"MaxAngularVelocity"	"220"
	}
	"Engine"
	{
		"HorsePower"			"150"
		"MaxRPM"				"2500"
		"MaxSpeed"				"30"
		"MaxReverseSpeed"		"21"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"6"
		"Gear"					"3.2"
		"Gear"					"2.4"
		"Gear"					"1.5"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"2300"
		"ShiftDownRPM"			"900"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"20"
		"DegreesBoost"						"11"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"22"
		"FastCarSpeed"						"60"
		"SlowSteeringRate"					"3"
		"FastSteeringRate"					"2"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.25"
		"BrakeSteeringRateFactor"			"3"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17"
			"Mass"							"300"
			"Inertia"						"0"
			"Damping"						"0.4"
			"RotDamping"					"0.3"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"jeeptire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"35"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4"
			"MaxBodyForce"					"16"
		}
		"TorqueFactor"						"0.25"
		"BrakeFactor"						"0.4"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17"
			"Mass"							"1000"
			"Inertia"						"0"
			"Damping"						"0.6"
			"RotDamping"					"0.1"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"jeeptire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"45"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"2"
			"MaxBodyForce"					"26"
		}
		"TorqueFactor"						"1.55"
		"BrakeFactor"						"0.4"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.15"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.6"
		"Speed_Approach_Factor"	"0.108"
	}
	"Gear"
	{
		"Max_Speed"				"0.98"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1.16"
		"Speed_Approach_Factor"	"0.015"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\perryn\pierce_pumper\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\perryn\pierce_pumper\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_off.mp3"
		"Min_Time"	"1"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_start.mp3"
		"Min_Time"	"1.54"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\perryn\pierce_pumper\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\idle.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\perryn\pierce_pumper\second.mp3"
		"Min_Time"	"1.25"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\perryn\pierce_pumper\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
