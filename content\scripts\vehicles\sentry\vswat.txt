"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"2"
		"MassCenterOverride"	"0 5 15"
		"MassOverride"			"16500"
		"AddGravity"			"1.66"
		"MaxAngularVelocity"	"100"
	}
	"Engine"
	{
		"HorsePower""2000"
		"MaxRPM""3550"
		"MaxSpeed""40"
		"MaxReverseSpeed""15"
		"AutobrakeSpeedGain""1.1"
		"AutobrakeSpeedFactor""3"
		"Autotransmission""0"
		"AxleRatio""3.9"
		"Gear""4.17"
		"Gear""2.34"
		"Gear""1.52"
		"Gear""1.14"
		"Gear""5.40"

		"ShiftUpRPM""2000"
		"ShiftDownRPM""1500"



	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"25"
		"DegreesBoost"						"5"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"25"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.5"
		"TurnThrottleReduceFast"			"1.5"
		"BrakeSteeringRateFactor"			"3"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"200"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18.5"
			"Mass"							"850"
			"Damping"					"0.0"
			"RotDamping"					"0"
			"Material"					"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"30"
			"SpringDamping"					"3.7"
			"StabilizerConstant"			"2.5"
			"SpringDampingCompression"		"2.5"
			"MaxBodyForce"					"2050"
		}
		"TorqueFactor"						"0.8"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18.5"
			"Mass"							"850"
			"Damping"					"0.1"
			"RotDamping"					"0"
			"Material"					"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"30"
			"SpringDamping"					"3.7"
			"StabilizerConstant"			"2.5"
			"SpringDampingCompression"		"2.5"
			"MaxBodyForce"					"2050"
		}
		"TorqueFactor"						"0.8"
		"BrakeFactor"						"0.5"
	}
}


"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.01"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.065"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.9"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.1"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.2"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/SGMCars/vswat/start.mp3"
		"Min_Time"	"0.50"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/SGMCars/vswat/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/SGMCars/vswat/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/SGMCars/vswat/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/SGMCars/vswat/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/SGMCars/vswat/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/SGMCars/vswat/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/SGMCars/vswat/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/SGMCars/vswat/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/SGMCars/vswat/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/SGMCars/vswat/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/SGMCars/vswat/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/SGMCars/vswat/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/SGMCars/vswat/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/SGMCars/vswat/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/SGMCars/vswat/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/SGMCars/vswat/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}