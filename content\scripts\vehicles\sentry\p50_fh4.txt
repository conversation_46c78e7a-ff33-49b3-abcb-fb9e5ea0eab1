// p50_fh4, created by [FL] Conn in Fri Jun 24 16:39:32 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.8"
		"MassCenterOverride"	"0 0 -5"
		"MassOverride"			"200"
		"AddGravity"			"0.66"
		"MaxAngularVelocity"	"400"
	}
	"Engine"
	{
		"HorsePower"			"3"
		"MaxRPM"				"5000"
		"MaxSpeed"				"15"
		"MaxReverseSpeed"		"2"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"3.9"
		"Gear"					"20"

		"ShiftUpRPM"			"4500"
		"ShiftDownRPM"			"3500"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"15"
		"DegreesBoost"						"5"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"5"
		"FastCarSpeed"						"15"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.2"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"3"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"350"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"4.5"
			"Mass"							"150"
			"Inertia"						"0.5"
			"Damping"						"0.2"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"phx_rubbertire2"
			"BrakeMaterial"					"phx_rubbertire2"
		}
		"Suspension"
		{
			"SpringConstant"				"115"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"4.25"
			"Mass"							"150"
			"Inertia"						"0.5"
			"Damping"						"0.4"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"phx_rubbertire2"
			"BrakeMaterial"					"phx_rubbertire2"
		}
		"Suspension"
		{
			"SpringConstant"				"50"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.8"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.32"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.6"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.94"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.7"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/sgmcars/p50_fh4/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/sgmcars/p50_fh4/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/sgmcars/p50_fh4/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/sgmcars/p50_fh4/start.mp3"
		"Min_Time"	"0.7"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/sgmcars/p50_fh4/drive.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/sgmcars/p50_fh4/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
