// suzuki_address, created by [<PERSON>] Conn in Fri Jun 24 15:36:03 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.75"
		"MassCenterOverride"	"0 -10 -2"
		"MassOverride"			"1000"
		"AddGravity"			"2.99"
		"MaxAngularVelocity"	"120"
	}
	"Engine"
	{
		"HorsePower"			"55"
		"MaxRPM"				"4500"
		"MaxSpeed"				"35"
		"MaxReverseSpeed"		"5"
		"AutobrakeSpeedGain"	"1.03"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"3.9"
		"Gear"					"4"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"2500"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"25"
		"DegreesBoost"						"5"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"15"
		"FastCarSpeed"						"30"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.1"
		"TurnThrottleReduceFast"			"0.25"
		"BrakeSteeringRateFactor"			"2"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"300"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"12.5"
			"Mass"							"250"
			"Inertia"						"0.3"
			"Damping"						"0.2"
			"RotDamping"					"0.2"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"phx_rubbertire2"
			"BrakeMaterial"					"phx_rubbertire2"
		}
		"Suspension"
		{
			"SpringConstant"				"95"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"0.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"12.5"
			"Mass"							"250"
			"Inertia"						"0.4"
			"Damping"						"0.3"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"phx_rubbertire2"
			"BrakeMaterial"					"phx_rubbertire2"
		}
		"Suspension"
		{
			"SpringConstant"				"170"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"0.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.75"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.15"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.5"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.7"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.2"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.7"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/sgmcars/impulsionbike/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/sgmcars/impulsionbike/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/sgmcars/impulsionbike/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/sgmcars/impulsionbike/idle2.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/sgmcars/impulsionbike/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/sgmcars/impulsionbike/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/sgmcars/impulsionbike/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/sgmcars/impulsionbike/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/sgmcars/impulsionbike/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/sgmcars/impulsionbike/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/sgmcars/impulsionbike/start.mp3"
		"Min_Time"	"0.9"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/sgmcars/impulsionbike/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/sgmcars/impulsionbike/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/sgmcars/impulsionbike/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/sgmcars/impulsionbike/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/sgmcars/impulsionbike/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/sgmcars/impulsionbike/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
