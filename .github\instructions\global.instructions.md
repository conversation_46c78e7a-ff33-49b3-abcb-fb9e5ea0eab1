---
applyTo: "**"
---

# CityRP Gamemode - Global Instructions

## Architecture Overview
CityRP is a bespoke Garry's Mod roleplay gamemode with a strict core-plugin architecture. The `cityrp` global table is the primary API namespace, replacing standard GMod patterns with modern alternatives.

**Critical Principle**: Core provides immutable APIs and GM: hooks. Plugins implement features using core APIs with strict isolation.

## Technology Stack
- **Language**: Garry's Mod <PERSON>a (gLua) - case-sensitive, dynamically typed
- **Database**: MySQL with automatic migrations (MySQLOO with SQLite fallback)
- **Dependencies**: niknaks, fn, mysqloo
- **Documentation**: LuaLS annotations for type safety

## Boot Sequence & File Loading
1. **Boot**: `gamemode/init.lua` → `shared.lua` → core system loading
2. **Third-party libraries**: niknaks, fn, mysqloo
3. **Core systems**: util, logging, networking, database, commands
4. **Plugin loading per directory**: languages → libs → items → plugins → derma → entities → itemspostload → migrations

**File Include System**:
- Use `cityrp.util.Include("filename.lua")` and `cityrp.util.IncludeDir("directory/")`
- Automatic realm detection via prefixes: `cl_` (client), `sv_` (server), `sh_` (shared)
- Load order matters: Libraries → Metatables → Items → Plugins → UI

## Core File Structure

**Core gamemode files** (gamemode/ directory):
- Entry points: cl_init.lua, init.lua, shared.lua
- Core APIs: gamemode/core/libraries/ contains all cityrp.* API implementations
- Player/Entity extensions: gamemode/core/metatables/ extends GMod objects with cityrp methods
- GM hooks: gamemode/core/hooks/ contains all GM: hook implementations (NEVER modify from plugins)
- Base items: gamemode/core/items/ defines foundational item types
- Database schema: gamemode/core/migrations/ contains core database changes

**Plugin structure** (plugins/<plugin_name>/ directory):
- Load order within plugin: languages/ → libs/ → items/ → plugins/ → derma/ → entities/ → itemspostload/ → migrations/
- Each plugin is completely isolated and self-contained
- Plugin migrations in plugins/<plugin_name>/migrations/ for plugin-specific database changes

**Critical boundary**: NEVER modify gamemode/ files from plugin code. Only use cityrp.* APIs.

## Essential Core APIs
- `cityrp.util` - File includes, utilities, formatting
- `cityrp.logging` - Structured logging system
- `cityrp.net` - Entity state networking (prefer over SetNW*)
- `cityrp.item` - Modern item system with action builder
- `cityrp.inventory` - Inventory management
- `cityrp.commandNew` - Command registration (prefer over legacy)
- `mysql` - Database queries with builder pattern

## Development Rules & Boundaries

### Plugin Isolation (CRITICAL)
- **Never modify core files** from plugins
- **No cross-plugin dependencies** - plugins must be self-contained
- **Use core APIs only** - don't access other plugin internals
- **Plugin hooks only** - use `PLUGIN:` or `hook.Add`, never `GM:`

### Modern vs Legacy Patterns
**Use Modern**: `cityrp.commandNew`, `ITEM:AddAction()`, `cityrp.net`, `mysql:Select()`, `cityrp.logging`
**Avoid Legacy**: old command system, OnEquip/canAction/onUse, SetNW*/GetNW*, direct SQL, print()/Msg()

### File Naming Conventions
- `cl_*.lua` - Client-side only
- `sv_*.lua` - Server-side only
- `sh_*.lua` - Shared (both realms)
- No prefix = shared by default

## Environment & Configuration

### Environment Detection
```lua
-- Server type: "main", "build", "beta"
if FLServer == "build" then
    -- Development behavior
end

-- Environment: "production", "development", etc.
if cityrp.env == "production" then
    -- Production-only logic
end
```

### Performance Optimizations
- `cityrpserver["DisablePrecache"] = true` - Development optimization
- Quick AutoRefresh suppresses logs during hot reload
- Items load in phases for faster startup

## Search & Refactoring Strategy
Before modifying any code:
1. **Semantic search** - Understand broader context and usage patterns
2. **List code usages** - Find all references to symbols being changed
3. **Grep search** - Locate exact string matches across codebase
4. **Check both gamemode/ and plugins/** - Ensure compatibility

Always preserve external API contracts while modernizing internal implementations.

## Key Locations to Check First
- `gamemode/core/sh_util.lua` - Existing utility functions
- `gamemode/core/libraries/` - Core API implementations
- `gamemode/core/metatables/` - Extended object methods
- `plugins/*/` - Plugin implementations using APIs

**Remember**: CityRP is bespoke - always search existing `cityrp.*` APIs before implementing new functionality. The gamemode replaces standard GMod patterns with modern, secure alternatives.


