// crsk_chevrolet_elcamino_1973, created by [FL] Conn in Fri Jun 24 16:05:11 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1"
		"MassCenterOverride"	"0 2 2.5"
		"MassOverride"			"1712"
		"AddGravity"			"1.3"
		"MaxAngularVelocity"	"100"
	}
	"Engine"
	{
		"HorsePower"			"205"
		"MaxRPM"				"5000"
		"MaxSpeed"				"48"
		"MaxReverseSpeed"		"15"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"5"
		"Gear"					"4"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"4500"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"5"
		"DegreesBoost"						"11"
		"FastDampen"						"80"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"5"
		"FastCarSpeed"						"60"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"2"
		"SteeringRestRateFast"				"1"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.3"
		"BrakeSteeringRateFactor"			"1.5"
		"ThrottleSteeringRestRateFactor"	"1.7"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"15.6"
			"Mass"							"500"
			"Inertia"						"0.05"
			"Damping"						"0.15"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"75"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"2.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"1.1"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"15.6"
			"Mass"							"550"
			"Inertia"						"0.05"
			"Damping"						"0.15"
			"RotDamping"					"0.1"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"65"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"2.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.1"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.25"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.5"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.73"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.1"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/ctv_sounds/el_camino/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/ctv_sounds/el_camino/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/ctv_sounds/el_camino/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/sgmcars\12charger_fm7/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/ctv_sounds/el_camino/first.mp3"
		"Min_Time"	"0.1"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/ctv_sounds/el_camino/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/ctv_sounds/el_camino/first.mp3"
		"Min_Time"	"0.1"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/ctv_sounds/el_camino/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/sgmcars/impala/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/ctv_sounds/el_camino/startup.mp3"
		"Min_Time"	"3"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/ctv_sounds/el_camino/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/ctv_sounds/el_camino/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/ctv_sounds/el_camino/first.mp3"
		"Min_Time"	"0.1"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/ctv_sounds/el_camino/first.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/ctv_sounds/el_camino/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/sgmcars\12charger_fm7/idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
