// vwcamper, created by <PERSON>M in 10/13/14 02:39:10, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 -6 2"
		"MassOverride"			"1200"
		"AddGravity"			"0.5"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"53"
		"MaxRPM"				"1500"
		"MaxSpeed"				"51"
		"MaxReverseSpeed"		"20"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"1"
		"AxleRatio"				"7"
		"Gear"					"7"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"11"
		"DegreesBoost"						"11"
		"FastDampen"						"35"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"2"
		"FastCarSpeed"						"32"
		"SlowSteeringRate"					"2.9"
		"FastSteeringRate"					"1.7"
		"SteeringRestRateSlow"				"2.9"
		"SteeringRestRateFast"				"1.7"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"1.7"
		"ThrottleSteeringRestRateFactor"	"1.7"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16"
			"Mass"							"400"
			"Inertia"						"0.6"
			"Damping"						"0"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"80"
			"SpringDamping"					"0.7"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"20"
		}
		"TorqueFactor"						"0"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16"
			"Mass"							"450"
			"Inertia"						"0.8"
			"Damping"						"0.05"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"100"
			"SpringDamping"					"0.7"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"26"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.4"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.06"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.38"
		"Speed_Approach_Factor"	"0.08"
	}
	"Gear"
	{
		"Max_Speed"				"0.59"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.77"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.01"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\tdmcars\beetle\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\tdmcars\beetle\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\tdmcars\beetle\rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\tdmcars\beetle\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\tdmcars\beetle\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\tdmcars\beetle\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\tdmcars\beetle\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\tdmcars\beetle\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\tdmcars\slsamg\engineoff.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\tdmcars\delorean\engineon.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\tdmcars\beetle\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\tdmcars\beetle\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\tdmcars\beetle\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\tdmcars\beetle\idle.mp3"
		"Min_Time"	"0.08"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\tdmcars\beetle\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\tdmcars\beetle\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}