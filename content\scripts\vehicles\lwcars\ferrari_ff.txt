// ferrari_ff, created by [FL] Conn in Fri Jun 24 16:24:57 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1"
		"MassCenterOverride"	"0 -2 1.5"
		"MassOverride"			"1800"
		"AddGravity"			"0.8"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"230"
		"MaxRPM"				"5000"
		"MaxSpeed"				"75"
		"MaxReverseSpeed"		"26"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"6"
		"Gear"					"6.4"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"2500"
	}
	"Steering"
	{
		"DegreesSlow"						"43"
		"DegreesFast"						"10"
		"DegreesBoost"						"11"
		"FastDampen"						"100"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"5"
		"FastCarSpeed"						"50"
		"SlowSteeringRate"					"3.3"
		"FastSteeringRate"					"2.1"
		"SteeringRestRateSlow"				"2.8"
		"SteeringRestRateFast"				"1.6"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.45"
		"BrakeSteeringRateFactor"			"2.2"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.2"
			"Mass"							"200"
			"Inertia"						"0"
			"Damping"						"0.2"
			"RotDamping"					"0.5"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"110"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"9"
			"MaxBodyForce"					"30"
		}
		"TorqueFactor"						"0.6"
		"BrakeFactor"						"0.65"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.2"
			"Mass"							"350"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0.8"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"130"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7"
			"MaxBodyForce"					"30"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.65"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.2"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.38"
		"Speed_Approach_Factor"	"0.052"
	}
	"Gear"
	{
		"Max_Speed"				"0.49"
		"Speed_Approach_Factor"	"0.034"
	}
	"Gear"
	{
		"Max_Speed"				"1.5"
		"Speed_Approach_Factor"	"0.033"
	}
	"Gear"
	{
		"Max_Speed"				"2"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/lwcars/ferrari_ff/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/lwcars/ferrari_ff/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/lwcars/ferrari_ff/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/lwcars/ferrari_ff/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/lwcars/ferrari_ff/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/lwcars/ferrari_ff/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/lwcars/ferrari_ff/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/lwcars/ferrari_ff/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/lwcars/ferrari_ff/startup.mp3"
		"Min_Time"	"4"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/lwcars/ferrari_ff/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/lwcars/ferrari_ff/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/lwcars/ferrari_ff/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/lwcars/ferrari_ff/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/lwcars/ferrari_ff/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/lwcars/ferrari_ff/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
