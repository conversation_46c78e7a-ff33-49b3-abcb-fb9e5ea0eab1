// ctv_hyundai_santa_fe, created by 𝓒𝓣𝓥 in Sun Jul 28 20:46:35 2019, using Vehicle Controller (VCMod).

"Vehicle"
{
 "WheelsPerAxle" "2"
 "Body"
 {
 "CounterTorqueFactor" "0.5"
 "MassCenterOverride" "0 -7 3"
 "MassOverride" "1893"
 "AddGravity" "1.3"
 "MaxAngularVelocity" "360"
 }
 "Engine"
 {
 "HorsePower" "174"
 "MaxRPM" "6300"
 "MaxSpeed" "95"
 "MaxReverseSpeed" "20"
 "AutobrakeSpeedGain" "1.1"
 "AutobrakeSpeedFactor" "3"
 "Autotransmission" "0"
 "AxleRatio" "20"
 "Gear" "4.1"

 "ShiftUpRPM" "3800"
 "ShiftDownRPM" "1600"
 }
 "Steering"
 {
 "DegreesSlow" "40"
 "DegreesFast" "5"
 "DegreesBoost" "11"
 "FastDampen" "80"
 "SteeringExponent" "0"
 "SlowCarSpeed" "5"
 "FastCarSpeed" "60"
 "SlowSteeringRate" "1.5"
 "FastSteeringRate" "1"
 "SteeringRestRateSlow" "3"
 "SteeringRestRateFast" "2"
 "TurnThrottleReduceSlow" "0.01"
 "TurnThrottleReduceFast" "0.6"
 "BrakeSteeringRateFactor" "1.5"
 "ThrottleSteeringRestRateFactor" "2"
 "BoostSteeringRestRateFactor" "1"
 "BoostSteeringRateFactor" "1.7"

 "PowerSlideAccel" "250"

 "SkidAllowed" "1"
 "DustCloud" "1"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "17.2"
 "Mass" "400"
 "Inertia" "3"
 "Damping" "0"
 "RotDamping" "0.7"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "95"
 "SpringDamping" "1"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "7"
 "MaxBodyForce" "20"
 }
 "TorqueFactor" "0.5"
 "BrakeFactor" "0.65"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "17.2"
 "Mass" "500"
 "Inertia" "3"
 "Damping" "0"
 "RotDamping" "0"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "95"
 "SpringDamping" "0.5"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "7"
 "MaxBodyForce" "30"
 }
 "TorqueFactor" "0.5"
 "BrakeFactor" "0.6"
 }
}

"Vehicle_Sounds"
{
 "Gear"
 {
 "Max_Speed" "0.02"
 "Speed_Approach_Factor" "1"
 }
 "Gear"
 {
 "Max_Speed" "0.28"
 "Speed_Approach_Factor" "0.08"
 }
 "Gear"
 {
 "Max_Speed" "0.68"
 "Speed_Approach_Factor" "0.05"
 }
 "Gear"
 {
 "Max_Speed" "0.92"
 "Speed_Approach_Factor" "0.035"
 }
 "Gear"
 {
 "Max_Speed" "1"
 "Speed_Approach_Factor" "0.01"
 }
 "State"
 {
 "Name" "SS_SHUTDOWN_WATER"
 "Sound" "atv_stall_in_water"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_2_RESUME"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/second.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_START_WATER"
 "Sound" "atv_start_in_water"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_3_RESUME"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/third.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_REVERSE"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/rev.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_IDLE"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/idle.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_1_RESUME"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_4"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/fourth_cruise.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_1"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN_HIGHSPEED"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/throttle_off.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_START_IDLE"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/startup.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_4_RESUME"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/fourth_cruise.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_3"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/third.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_0_RESUME"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/first.mp3"
 "Min_Time" "0.75"
 }
 "State"
 {
 "Name" "SS_GEAR_0"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/first.mp3"
 "Min_Time" "0.08"
 }
 "State"
 {
 "Name" "SS_GEAR_2"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/second.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN"
 "Sound" "vehicles/ctvehicles/hyundai_santafe/idle.mp3"
 "Min_Time" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "350"
 "Min_Speed_Change" "250"
 "Sound" "atv_impact_medium"
 "Gear_Limit" "1"
 }
 "CrashSound"
 {
 "Min_Speed" "450"
 "Min_Speed_Change" "350"
 "Sound" "atv_impact_heavy"
 "Gear_Limit" "0"
 }

 "Skid_LowFriction" ""
 "Skid_NormalFriction" ""
 "Skid_HighFriction" ""
}
