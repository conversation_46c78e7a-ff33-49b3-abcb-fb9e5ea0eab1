--[[
BaseItem Metatable
The BaseItem/ItemCore metatable is used for registering items on gamemode startup, see the Item metatable for how to manipulate items.
The ITEM metatable is a bit weird, having some quirks.
Typically, functions which relate to generating the items / setting data (New, SetDescription) are in UpperCamelCase.
However, functions which relate to the operation of the item (canUse, onDrop), are in lowerCamelCase.
@copyright 2015-2022 Limelight Gaming
@release CityRP 2
<AUTHOR>
@item ITEM
--]]

--- The base item metatable for all items.
--- @class base_item
--- @field actions table<string, item_action>
--- @field GetCost fun(self: self): integer Gets the Market Cost.
--- @field SetCost fun(self: self, cost: integer): self Sets the Market Cost.
--- @field SetPrice fun(self: self, data: integer): self Alias of SetCost.
--- @field GetPrice fun(self: self): integer Alias of GetCost.
--- @field SetDescription fun(self: self, data: string): self Sets the Description of an item.
--- @field GetDescription fun(self: self): string Gets an item's description.
--- @field SetDesc fun(self: self, data: string): self Alias of SetDescription.
--- @field GetDesc fun(self: self): string Alias of GetDescription.
--- @field SetCategory fun(self: self, data: string): self Sets the category of an item.
--- @field GetCategory fun(self: self): string Gets an item's category.
--- @field SetCat fun(self: self, data: string): self Alias of SetCategory.
--- @field GetCat fun(self: self): string Alias of GetCategory.
--- @field SetTeam fun(self: self, data: integer): self Sets the team of an item.
--- @field GetTeam fun(self: self): integer Gets an item's team.
--- @field SetBatch fun(self: self, data: integer): self Sets the number of items created in a press of the manufacture button.
--- @field GetBatch fun(self: self): integer Gets an item's batch.
--- @field SetSize fun(self: self, data: integer): self Sets the inventory size of this item.
--- @field GetSize fun(self: self): integer Gets an item's inventory size.
--- @field SetInitialStock fun(self: self, data: integer): self Sets the initial dynamic economy stock for this item.
--- @field GetInitialStock fun(self: self): integer Gets the initial dynamic economy stock for this item.
--- @field SetSupporter fun(self: self, data: boolean): self Sets if the item is for supporters.
--- @field GetSupporter fun(self: self): boolean Gets if the item is for supporters.
--- @field SetSupporterPlus fun(self: self, data: boolean): self Sets if the item is for supporter plus.
--- @field GetSupporterPlus fun(self: self): boolean Gets if the item is for supporter plus.
--- @field onUse? fun(self: any, item: Item, client: Player): boolean Called when the item is being used.
--- @field canUse? fun(self: any, item: Item, client: Player): boolean Called to check if the item can be used.
--- @field onRemove? fun(self: any, client: Player, item: Item): boolean Called when the item is being removed.
--- @field canRemove? fun(self: any, client: Player, item: Item): boolean Called to check if the item can be removed.
--- @field onDrop? fun(self: any, item: Item, client: Player, pos: Vector, amount: integer): boolean Called when the item is being dropped.
--- @field canDrop? fun(self: any, item: Item, client: Player, pos: Vector, amount: integer): boolean Called to check if the item can be dropped.
--- @field onGive? fun(self: any, item: Item, client: Player, target: Player, amount: integer): boolean Called when the item is being given.
--- @field canGive? fun(self: any, item: Item, client: Player, target: Player, amount: integer): boolean Called to check if the item can be given..
--- @field canTransfer? fun(self: any, item: Item, client: Player, invFrom: Inventory, invTo: Inventory): boolean Called to check if the item can be transferred.
--- @field sort? fun(self: self, table: table): table Optional custom sort to decide order of items returned via HasItems
--- @field PlayerDeathHook? fun(self: self, item: Item, client: Player) Called when a player dies with this item in their primary inventory
--- @field ItemRegistered? fun(self: self) Called when this item is registered via the ItemCore system
--- @field ItemCreated? fun(self: self, item: Item) Called when this item is created as an instanced item
ITEM = {
	name = "Base Item",
	description = "This is a base item. You shouldn't see it, or have it.",
	size = 1,
	cost = 0,
	model = "models/error.mdl",
	batch = 5,
	store = false,
	stackable = false,
	category = "Base",
	skin = 0,
}

--- Item Name.
-- @fstring name The localised name, as shown in the F1 menu.

--- Description of the item.
-- @fstring description

--- Item Size
-- @fnumber size The inventory space used per item.

--- Item Cost
-- @fint cost The cost of the item as bought in the market.
-- Also used for inventory valuations and refund sales.

--- Model Path
-- @fstring model Path of the model used in the F1 menu / dropped states.

--- Market Availability
-- @fbool store

--- Inventory / Market Category
-- @fstring category

--- Skin used in the F1 menu.
-- @fint skin

--- Create a new item.
-- @string base UniqueID of the baseclass.
-- @tab data Optional data to fill into the item.
-- @chainable

--- @generic T : base_item
--- @param base `T` # UniqueID of the item base to use
--- @param data table|nil # Optional table of data
--- @return T|base_item # Created item class
function ITEM:New(base, data)
	local item
	if isstring(base) then
		item = cityrp.item.new(base)
	elseif isstring(data) then
		item = cityrp.item.new(data)
	else
		item = cityrp.item.new()
	end

	if istable(base) then
		item:Merge(base)
	elseif istable(data) then
		item:Merge(data)
	end

	return item
end

local function fileitem()
	local str = debug.getinfo(3, "S").source:sub(2)
	return str:match("^.*/[^_]*_(.*).lua$") or str
end

--- Clone an item.
-- Returns a new ITEM, with base set to the first item.
-- @chainable
function ITEM:Clone()
	return setmetatable({ base = rawget(self, "uniqueID") or "tmp_base_" .. fileitem() }, { __index = self })
end

--- Merge a table of data into the item.
-- @tab data Key/Value pairs of data.
-- @chainable
function ITEM:Merge(data)
	if data and istable(data) then
		self = table.Merge(self, data)
	end

	return self
end

--- Set a single key on the item.
-- @string name Key to set.
-- @param value Value to set.
-- @deprecated
-- @see Set
-- @chainable
function ITEM:SetData(name, value)
	ErrorNoHaltWithStack("ITEM:SetData() is deprecated. Use ITEM:Set()")
	return self:Set(name, value)
end

--- Set a single key on the item.
-- @string name Key to set.
-- @param value Value to set.
-- @chainable
function ITEM:Set(name, value)
	self[name] = value
	return self
end

--- Register an item.
-- <strong style='color:red'>This must be the last call in an item chain, as it does processing on the item.</strong>
-- @string[opt] id UniqueID to set, not required if set prior.
function ITEM:Register(id)
	if id then
		self.uniqueID = id
	end

	return cityrp.item.register(self)
end

--- Register an item, attempting to pull the item's UID from the file name.
-- @see Register
function ITEM:AutoRegister()
	return self:Register(fileitem())
end

--- Add a localisation variable to a given field.
-- This is fed into L.
-- @string field The name of the field to add the localisation value for.
-- @param value Value to pass to L.
-- @chainable
function ITEM:AddLocalisationValue(field, value)
	if not rawget(self, "localisationArgs") then
		if self.localisationArgs then
			self.localisationArgs = table.Copy(self.localisationArgs)
		else
			self.localisationArgs = {}
		end
	end

	if not self.localisationArgs[field] then
		self.localisationArgs[field] = {}
	end

	table.insert(self.localisationArgs[field], value)
	return self
end

--- Creates an automatically named accessor function pair.
-- It also returns a single param function, which sets a default value and returns the item.
-- @usage local setDefault = ITEM:AutoFunction("SpecialValue", "internal_flag")
-- -- This creates ITEM:SetSpecialValue() and ITEM:GetSpecialValue().
-- -- Calling this function sets a value on ITEM.internal_flag.
-- -- Calling setDefault, sets the value now, returning the item. This is useful for chaining.
-- ITEM:AutoFunction("SomeValue", "this_defaults_to_false")(false)
-- @string name Function Key (Resolves to [GS]et<Name>).
-- @string key Internal key to set.
-- @see gmod(AccessorFunc)
-- @treturn function
---@return fun(data: any): base_item
function ITEM:AutoFunction(name, key)
	local setter = Format("Set%s", name)
	self[setter] = function(item, data)
		item[key] = data
		return item
	end

	local getter = Format("Get%s", name)
	self[getter] = function(item)
		return item[key]
	end

	return function(data) return self[setter](self, data) end
end

local datas = {
	--- Sets the Market Cost.
	-- @function ITEM:SetCost
	-- @int cost Cost.
	-- @chainable

	--- Gets the Market Cost.
	-- @function ITEM:GetCost
	-- @rint Item's Cost
	Cost = "cost",
	--- Alias of ITEM:SetCost
	-- @function ITEM:SetPrice
	-- @int data Cost.
	-- @see SetCost
	-- @chainable

	--- Alias of ITEM:GetCost
	-- @function ITEM:GetPrice
	-- @rint Item's Cost
	-- @see GetCost
	Price = "cost",
	--- Sets the Description of an item.
	-- @function ITEM:SetDescription
	-- @string data New Description
	-- @chainable

	--- Gets an item's description
	-- @function ITEM:GetDescription
	-- @rstring Item's Description
	Description = "description",
	--- Alias of ITEM:SetDescription
	-- @function ITEM:SetDesc
	-- @string data New Description
	-- @see ITEM:SetDescription
	-- @chainable

	--- Alias of ITEM:GetDescription
	-- @function ITEM:GetDesc
	-- @rstring Item's Description
	-- @see GetDescription
	Desc = "description",
	--- Sets the category of an item.
	-- @function ITEM:SetCategory
	-- @string data New category
	-- @chainable

	--- Gets an item's category
	-- @function ITEM:GetCategory
	-- @rstring Item's Category
	Category = "category",
	--- Alias of ITEM:SetCategory
	-- @function ITEM:SetCat
	-- @string data New category
	-- @see ITEM:SetCategory
	-- @chainable

	--- Gets an item's category
	-- @function ITEM:GetCategory
	-- @rstring Item's Category
	-- @see ITEM:GetCategory
	Cat = "category",
	--- Sets the team of an item.
	-- @function ITEM:SetTeam
	-- @int data Team Index
	-- @chainable

	--- Gets an item's team
	-- @function ITEM:GetTeam
	-- @rint Item's Team.
	Team = "team",
	--- Sets the number of items created in a press of the manufacture button.
	-- @todo Check if this is still used, with the advent of deco.
	-- @function ITEM:SetBatch
	-- @int data Batch
	-- @chainable

	--- Gets an item's batch
	-- @function ITEM:GetBatch
	-- @rint Batch Size
	Batch = "batch",
	--- Sets the inventory size of this item.
	-- @function ITEM:SetSize
	-- @int data Size
	-- @chainable

	--- Gets an item's inventory size.
	-- @function ITEM:GetSize
	-- @rint Size
	Size = "size",

	--- Sets the initial dynamic economy stock for this item.
	-- @function ITEM:SetInitialStock
	-- @int data Initial Stock Count
	-- @chainable

	--- Gets the initial dynamic economy stock for this item.
	-- @function ITEM:GetInitialStock
	-- @rint Stock
	InitialStock = "initStock",

	--- Sets if the item is for supporters.
	-- @function ITEM:SetSupporter
	-- @bool data Supporter Required.
	-- @chainable

	--- Gets if the item is for supporters.
	-- @function ITEM:GetSupporter
	-- @rbool If supporter is required.
	Supporter = "supporter",

	--- Sets if the item is for supporter plus.
	-- @function ITEM:SetSupporterPlus
	-- @bool data Supporter Required.
	-- @chainable

	--- Gets if the item is for supporter plus.
	-- @function ITEM:GetSupporterPlus
	-- @rbool If supporter plus is required.
	SupporterPlus = "supporterplus",
}
for name, key in pairs(datas) do
	ITEM:AutoFunction(name, key)
end

--- Fill in as many fields as possible with localisation.
-- @chainable
function ITEM:AutoLocalise()
	return self
		:SetName("@", "@")
		:SetDescription("@")
end

--- Sets the name of an item.
-- If plural is undefined, defaults to "<name>s"
-- If plural is true, it's set to "<name>"
-- @string name New name.
-- @tparam[opt=<name>s] string|false plural Optional plural to set.
-- @chainable
function ITEM:SetName(name, plural)
	self.name = name
	return self:SetPlural(plural)
end

--- Gets an item's name.
-- @rstring Name
function ITEM:GetName()
	return self.name
end

--- Sets the model of the item.
-- If model is true, the model and skin will be pulled from the store model.
-- If autostore is true, model and skin will be pushed to the store model.
-- @tparam string|true model Model path.
-- @int skin Skin index.
-- @bool[opt] autoStore Should this call also automatically set the store models.
-- @chainable
function ITEM:SetModel(model, skin, autoStore)
	if model == true then
		self.model = self.storemodel
		self.skin = self.storeskin
	else
		self.model = model
		self.skin = skin
	end

	if autoStore then
		self.storemodel = self.model
		self.storeskin = self.skin
	end

	return self
end

--- Get an item's model.
-- @rstring Model path.
function ITEM:GetModel()
	return self.model
end

--- Sets the store model of the item.
-- If model is true, the model and skin will be pulled from the regular model.
-- @tparam string|true model Model path.
-- @int skin Skin index.
-- @chainable
function ITEM:SetStoreModel(model, skin)
	if model == true then
		self.storemodel = self.model
		self.storeskin = self.skin
	else
		self.storemodel = model
		self.storeskin = skin
	end

	return self
end

--- Get an item's model in the market / inventory.
-- @rstring Model path.
function ITEM:GetStoreModel()
	return self.storemodel
end

--- Sets the skin of the item.
-- If skin is true, it is pulled from the store skin.
-- @tparam int|true skin skin
-- @bool[opt] autoStore If skin should be pushed to store skin.
function ITEM:SetSkin(skin, autoStore)
	if skin == true then
		self.skin = self.storeskin
	else
		self.skin = skin
	end

	if autoStore then
		self.storeskin = self.skin
	end

	return self
end

--- Get an item's skin.
-- @rint Model skin index.
function ITEM:GetSkin()
	return self.skin
end

--- Sets the store skin of the item.
-- If skin is true, it is pulled from the item skin.
-- @tparam int|true skin skin
-- @bool[opt] autoStore If skin should be pushed to item skin.
function ITEM:SetStoreSkin(skin, autoStore)
	if skin == true then
		self.storeskin = self.skin
	else
		self.storeskin = skin
	end

	if autoStore then
		self.skin = self.storeskin
	end

	return self
end

--- Get an item's skin in the store page.
-- @rint Model skin index.
function ITEM:GetStoreSkin()
	return self.storeskin
end

--- Sets the item's plural.
-- If nil/false, item's name + s is used.
-- If true, item's name is used.
-- Otherwise passed name is used.
-- @tparam[opt] string|true name
-- @chainable
function ITEM:SetPlural(name)
	if not name then
		self.plural = self.name .. "s"
	elseif not isstring(name) then
		self.plural = self.name
	elseif name == "" then
		self.plural = false
	else
		self.plural = name
	end

	return self
end

--- Gets an item plural.
-- @rstring Pluralised name.
function ITEM:GetPlural()
	return self.plural
end

--- Set if the item is in store, and its batch amount.
-- If boolean, store is directly set.
-- If 0, store is set to false.
-- Otherwise store is set true and the value
-- @tparam bool|int val Store Setting / Batch Amount
-- @chainable
function ITEM:SetStore(val)
	if not val or val == 0 then
		self.store = false
	elseif isnumber(val) then
		self.store = true
		self.batch = val
	elseif isbool(val) then
		self.store = val
	end

	return self
end

--- Set the item as stackable
-- This means it can never contain unique data and adds a stack count
-- Items will be able to stack up to the MaxStack value
-- This is intended for use by simple items that never need unique data
-- By default, an item not stackable
-- @tparam bool val Unique Status
-- @chainable
function ITEM:SetStackable(val)
	self.stackable = val
	return self
end

--- Set the items max stack
-- This is the maximum amount of items that can be stacked
-- @tparam int val Max Stack
-- @chainable
function ITEM:SetMaxStack(val)
	self.maxstack = val
	return self
end

--- Get the item can be bought in the store.
-- @rbool Buyable status.
function ITEM:GetStore()
	return self.store
end

--- Marks an item as untradable.
-- This disables dropping and giving, but can be overwritten later.
-- @chainable
function ITEM:Untradable()
	return self
		:Set("onDrop", false)
		:Set("onGive", false)
end

local defaultFunc = function() return true end

--- @class item_action
--- @field id string
--- @field name string
--- @field icon string
--- @field sortRank integer
--- @field condition fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
--- @field action fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
--- @field onMenuOption? fun(actionMenu: DMenu, item: Item)


--- @class item_action_builder
--- @field private id string
--- @field private name string
--- @field private icon string
--- @field private condition fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
--- @field private action fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
--- @field private data table
--- @field private item base_item
ITEM_ACTION = {}
ITEM_ACTION.data = {}
ITEM_ACTION.icon = "icon16/brick.png"
ITEM_ACTION.condition = defaultFunc
ITEM_ACTION.action = defaultFunc


---@param name string
function ITEM_ACTION:SetName(name)
	self.name = name
	return self
end

---@param rank integer
function ITEM_ACTION:SetSortRank(rank)
	self.sort = rank
	return self
end

---@param icon string
function ITEM_ACTION:SetIcon(icon)
	self.icon = icon
	return self
end

---@param func fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
function ITEM_ACTION:SetCondition(func)
	self.condition = func
	return self
end

---@param func fun(baseItem: base_item, item: Item, client: Player, ...: any): boolean
function ITEM_ACTION:SetAction(func)
	self.action = func
	return self
end

---Creates the action, automatically registers it to this item and returns the action
---@return item_action
function ITEM_ACTION:Build()

	self.item.actions[self.id] = {
		["id"] = self.id,
		["name"] = self.name or self.id,
		["icon"] = self.icon or "icon16/brick.png",
		["condition"] = self.condition or defaultFunc,
		["action"] = self.action or defaultFunc,
		["sortRank"] = self.sort or 0,
	}
	return self.item.actions[self.id]
end

ITEM_ACTION.__index = ITEM_ACTION

---@alias item_action_ids
---| '"Use"'
---| '"Remove"'
---| '"Drop"'
---| '"Give"'
---| '"Place"'
---| '"Pickup"'
---| '"Equip"'
---| '"Unequip"'
---| '"Pickup"'

---Add an action to this item
---@param id item_action_ids|string ID name for this action
---@return item_action_builder action
function ITEM:AddAction(id)
	local action
	action = setmetatable({ item = self, id = id }, { __index = ITEM_ACTION })
	return action
end

---Disable the automated action conversion for legacy items
---@return self
function ITEM:DisableAutoActions()
	self.noAutoAction = true
	return self
end

-- Sets if a item is replaced.
-- If called with no args, replaced flag is cleared.
-- @string[opt] by Unique ID of replaced item.
-- @chainable
-- function ITEM:Replaced(by)
-- 	if by then
-- 		self.replaced = true
-- 		self.replacedid = by
-- 	else
-- 		self.replaced = nil
-- 		self.replacedid = nil
-- 	end
-- 	return self
-- end

-- Create a simple replacement item.
-- <strong style='color:red'>new's item must be registered before the replacement call.</strong>
-- @string new Unique ID of the replacement.
-- @string old Unique ID of the old item.
-- function ITEM:Replacement(new, old)
-- 	return self:New(new):Replaced(new):Register(old)
-- end

--- The lookup index for the item metatable.
-- @ftab __index
ITEM.__index = ITEM

--- @alias ITEM base_item
