// crsk_honda_civic_typer_fk8_2017, created by [FL] Conn in Sat Jun 25 13:59:25 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.7"
		"MassCenterOverride"	"0 -10 7"
		"MassOverride"			"1980"
		"AddGravity"			"0.8"
		"MaxAngularVelocity"	"350"
	}
	"Engine"
	{
		"HorsePower"			"325"
		"MaxRPM"				"5000"
		"MaxSpeed"				"75"
		"MaxReverseSpeed"		"27"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"6"
		"Autotransmission"		"0"
		"AxleRatio"				"7.2"
		"Gear"					"4.8"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"7"
		"DegreesBoost"						"11"
		"FastDampen"						"90"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"2"
		"FastCarSpeed"						"60"
		"SlowSteeringRate"					"2.9"
		"FastSteeringRate"					"1.8"
		"SteeringRestRateSlow"				"2.9"
		"SteeringRestRateFast"				"1.8"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"2.9"
		"ThrottleSteeringRestRateFactor"	"1.8"
		"BoostSteeringRestRateFactor"		"1.8"
		"BoostSteeringRateFactor"			"1.8"

		"PowerSlideAccel"					"135"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"13.7"
			"Mass"							"220"
			"Inertia"						"0"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"150"
			"SpringDamping"					"0.8"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"6"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.05"
		"BrakeFactor"						"0.6"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"13.7"
			"Mass"							"250"
			"Inertia"						"2"
			"Damping"						"0.4"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"170"
			"SpringDamping"					"0.8"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"6"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.7"
		"BrakeFactor"						"0.55"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.15"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.25"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.44"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.7"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/sgmcars/mx5/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/sgmcars/mx5/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/sgmcars/mx5/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/sgmcars/mx5/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/sgmcars/mx5/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/sgmcars/mx5/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/sgmcars/mx5/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/sgmcars/mx5/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/sgmcars/mx5/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/sgmcars/mx5/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/sgmcars/mx5/start.mp3"
		"Min_Time"	"0.6"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/sgmcars/mx5/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/sgmcars/mx5/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/sgmcars/mx5/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/sgmcars/mx5/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/sgmcars/mx5/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/sgmcars/mx5/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
