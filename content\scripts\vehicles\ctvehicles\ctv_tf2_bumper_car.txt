// ctv_tf2_bumper_car, created by 󠀡󠀡⁧⁧ 𝓒𝓣𝓥 in Fri Jul 30 18:59:32 2021, using Vehicle Controller (VCMod).

"Vehicle"
{
 "WheelsPerAxle" "2"
 "Body"
 {
 "CounterTorqueFactor" "0"
 "MassCenterOverride" "0 -7 3"
 "MassOverride" "500"
 "AddGravity" "1"
 "MaxAngularVelocity" "180"
 }
 "Engine"
 {
 "HorsePower" "5"
 "MaxRPM" "333"
 "MaxSpeed" "45"
 "MaxReverseSpeed" "10"
 "AutobrakeSpeedGain" "1.1"
 "AutobrakeSpeedFactor" "6"
 "Autotransmission" "0"
 "AxleRatio" "10"
 "Gear" "4.1"

 "ShiftUpRPM" "3800"
 "ShiftDownRPM" "1600"

 "Boost"
 {
 "Force" "1"
 "Duration" "5"
 "Delay" "10"
 "TorqueBoost" "1"
 "MaxSpeed" "90"
 }
 }
 "Steering"
 {
 "DegreesSlow" "40"
 "DegreesFast" "10"
 "DegreesBoost" "8"
 "FastDampen" "800"
 "SteeringExponent" "0"
 "SlowCarSpeed" "5"
 "FastCarSpeed" "60"
 "SlowSteeringRate" "2"
 "FastSteeringRate" "1"
 "SteeringRestRateSlow" "3"
 "SteeringRestRateFast" "2"
 "TurnThrottleReduceSlow" "0.01"
 "TurnThrottleReduceFast" "0.01"
 "BrakeSteeringRateFactor" "1.8"
 "ThrottleSteeringRestRateFactor" "2"
 "BoostSteeringRestRateFactor" "3"
 "BoostSteeringRateFactor" "1.7"

 "PowerSlideAccel" "250"

 "SkidAllowed" "1"
 "DustCloud" "1"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "5"
 "Mass" "259"
 "Inertia" "0.05"
 "Damping" "0"
 "RotDamping" "0"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "55"
 "SpringDamping" "1"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "7"
 "MaxBodyForce" "20"
 }
 "TorqueFactor" "0.3"
 "BrakeFactor" "0.6"
 }
 "Axle"
 {
 "Wheel"
 {
 "Radius" "5"
 "Mass" "250"
 "Inertia" "0.05"
 "Damping" "0"
 "RotDamping" "0"
 "Material" "phx_rubbertire2"
 "SkidMaterial" "slidingrubbertire"
 "BrakeMaterial" "brakingrubbertire"
 }
 "Suspension"
 {
 "SpringConstant" "55"
 "SpringDamping" "0.5"
 "StabilizerConstant" "0"
 "SpringDampingCompression" "7"
 "MaxBodyForce" "30"
 }
 "TorqueFactor" "0.15"
 "BrakeFactor" "0.6"
 }
}

"Vehicle_Sounds"
{
 "Gear"
 {
 "Max_Speed" "0.02"
 "Speed_Approach_Factor" "1"
 }
 "Gear"
 {
 "Max_Speed" "0.25"
 "Speed_Approach_Factor" "0.08"
 }
 "Gear"
 {
 "Max_Speed" "0.5"
 "Speed_Approach_Factor" "0.05"
 }
 "Gear"
 {
 "Max_Speed" "0.87"
 "Speed_Approach_Factor" "0.035"
 }
 "Gear"
 {
 "Max_Speed" "1"
 "Speed_Approach_Factor" "0.01"
 }
 "State"
 {
 "Name" "SS_SHUTDOWN_WATER"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/stop.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_2_RESUME"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_3_RESUME"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_REVERSE"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_IDLE"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/idle.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_1_RESUME"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_4"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_1"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN_HIGHSPEED"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/throttle_off.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_TURBO"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/turbo.mp3"
 "Min_Time" "6"
 }
 "State"
 {
 "Name" "SS_SHUTDOWN"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/stop.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_START_IDLE"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/startup.mp3"
 "Min_Time" "1.7"
 }
 "State"
 {
 "Name" "SS_GEAR_4_RESUME"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_3"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_GEAR_0_RESUME"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.75"
 }
 "State"
 {
 "Name" "SS_GEAR_0"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0"
 }
 "State"
 {
 "Name" "SS_GEAR_2"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/first.mp3"
 "Min_Time" "0.5"
 }
 "State"
 {
 "Name" "SS_SLOWDOWN"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/slowdown.mp3"
 "Min_Time" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "20"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit1.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "4"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit2.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "6"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit3.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "8"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit4.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "10"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit5.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "12"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit6.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "14"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit7.mp3"
 "Gear_Limit" "0"
 }
 "CrashSound"
 {
 "Min_Speed" "16"
 "Min_Speed_Change" "200"
 "Sound" "vehicles/ctvehicles/tf2_bumper_car/hit8.mp3"
 "Gear_Limit" "0"
 }

 "Skid_LowFriction" "vehicles/ctvehicles/tf2_bumper_car/skid.mp3"
 "Skid_NormalFriction" "vehicles/ctvehicles/tf2_bumper_car/skid.mp3"
 "Skid_HighFriction" "vehicles/ctvehicles/tf2_bumper_car/skid.mp3"
}
