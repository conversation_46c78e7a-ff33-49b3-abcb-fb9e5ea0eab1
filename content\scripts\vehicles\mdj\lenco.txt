// Lenco, created by [<PERSON>] <PERSON>n in Fri Jun 24 16:49:36 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0"
		"MassCenterOverride"	"0 -7 22"
		"MassOverride"			"10000"
		"AddGravity"			"2"
		"MaxAngularVelocity"	"100"
	}
	"Engine"
	{
		"HorsePower"			"500"
		"MaxRPM"				"1500"
		"MaxSpeed"				"65"
		"MaxReverseSpeed"		"20"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"1"
		"AxleRatio"				"4"
		"Gear"					"10"
		"Gear"					"3"
		"Gear"					"2"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"1400"
		"ShiftDownRPM"			"1000"
	}
	"Steering"
	{
		"DegreesSlow"						"30"
		"DegreesFast"						"5"
		"DegreesBoost"						"3"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"15"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"1"
		"TurnThrottleReduceFast"			"2"
		"BrakeSteeringRateFactor"			"2"
		"ThrottleSteeringRestRateFactor"	"2.1"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"19"
			"Mass"							"500"
			"Inertia"						"2"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"110"
			"SpringDamping"					"1"
			"StabilizerConstant"			"1"
			"SpringDampingCompression"		"4"
			"MaxBodyForce"					"2000"
		}
		"TorqueFactor"						"0.8"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"19"
			"Mass"							"500"
			"Inertia"						"2"
			"Damping"						"0.2"
			"RotDamping"					"0.1"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"110"
			"SpringDamping"					"1"
			"StabilizerConstant"			"1"
			"SpringDampingCompression"		"4"
			"MaxBodyForce"					"2000"
		}
		"TorqueFactor"						"0.8"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.15"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.6"
		"Speed_Approach_Factor"	"0.108"
	}
	"Gear"
	{
		"Max_Speed"				"0.98"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1.16"
		"Speed_Approach_Factor"	"0.015"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\perryn\pierce_pumper\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\perryn\pierce_pumper\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_off.mp3"
		"Min_Time"	"1"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\perryn\pierce_pumper\engine_start.mp3"
		"Min_Time"	"1.54"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\perryn\pierce_pumper\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\perryn\pierce_pumper\idle.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\perryn\pierce_pumper\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\perryn\pierce_pumper\second.mp3"
		"Min_Time"	"1.25"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\perryn\pierce_pumper\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
