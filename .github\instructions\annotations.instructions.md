---
applyTo: "**/*.lua"
---

# LuaLS Annotations Instructions

Scope: Convert existing documentation comments to LuaLS format across all Lua files without altering code behavior.

When to use: Any time you encounter LDoc-style tags or missing/incorrect annotations in Lua files.

## Conversion Rules
1. Identify comment lines containing LDoc tags (starting with `@`)
2. Replace prefix and tag with LuaLS equivalent (prefixed with `---`)
3. Preserve all content after the tag (variable names, types, descriptions)
4. Focus on lines with clear LDoc tags, ignore regular comments
5. Convert LDoc parameter types by rearranging parameter order
6. Maintain original code structure - don't condense or expand comment blocks
7. Preserve code being documented - only change documentation format
8. Keep existing triple-dash (`---`) descriptions as-is
9. Don't touch existing correct LuaLS annotations
10. Add annotations to missing functions using LuaLS format
11. Fix incorrect or incomplete existing annotations

## Parameter Type Conversions

### LDoc vs LuaLS Style:
- **LDoc:** `-- @tparam <type> <name> <description>`
- **LuaLS:** `--- @param <name> <type> <description>`

### Key Differences:
- `@param` in LDoc lacks type, LuaLS requires it (add `any` if missing)
- `@tparam` in LDoc has type before name, <PERSON><PERSON><PERSON> has type after name
- Specialized type tags convert to `@param` with appropriate type

## Complete Mapping Reference

### Parameters:
- `-- @param name desc` → `--- @param name any desc`
- `-- @tparam string name desc` → `--- @param name string desc`
- `-- @string name desc` → `--- @param name string desc`
- `-- @number name desc` → `--- @param name number desc`
- `-- @integer name desc` → `--- @param name integer desc`
- `-- @bool name desc` → `--- @param name boolean desc`
- `-- @tab name desc` → `--- @param name table desc`
- `-- @array name desc` → `--- @param name any[] desc`
- `-- @func name desc` → `--- @param name function desc`
- `-- @entity name desc` → `--- @param name Entity desc`
- `-- @player name desc` → `--- @param name Player desc`

### Returns:
- `-- @return desc` → `--- @return any desc`
- `-- @treturn string desc` → `--- @return string desc`
- `-- @treturn[1]` → `--- @return` (numbered returns)
- `-- @treturn[2]` → `--- @return` (numbered returns)

### Classes and Fields:
- `-- @class Name` → `--- @class Name`
- `-- @field name type desc` → `--- @field name type desc`
- `-- @table name` → `--- @class name` (table-based classes)

### Other Tags:
- `-- @type type` → `--- @type type`
- `-- @module name` → `--- @module name`
- `-- @alias name` → `--- @alias name`
- `-- @usage example` → `--- @usage example`
- `-- @deprecated reason` → `--- @deprecated reason`
- `-- @realm type` → `--- @realm type`
- `-- @see ref` → `--- @see ref`
- `-- @internal` → `--- @internal`
- `-- @generic T` → `--- @generic T`
- `-- @operator` → `--- @operator`
- `-- @vararg` → `--- @vararg`
- `-- @meta` → `--- @meta`
- `-- @async` → `--- @async`
- `-- @enum` → `--- @enum`
- `-- @function name` → `--- @function name`
- `-- @overload fun(params):return` → `--- @overload fun(params):return`

## CityRP Style Examples

### Example 1 - Basic Function:
**Before:**
```lua
--- Does something cool with an item.
-- @param ply The player who triggered the action.
-- @tparam string itemID The ID of the item to use.
-- @return True on success, false otherwise.
function cityrp.UseItem(ply, itemID)
```

**After:**
```lua
--- Does something cool with an item.
--- @param ply any The player who triggered the action.
--- @param itemID string The ID of the item to use.
--- @return boolean True on success, false otherwise.
function cityrp.UseItem(ply, itemID)
```

### Example 2 - Type-Specific Parameters:
**Before:**
```lua
--- Creates a new item instance.
-- @string uniqueID The unique ID of the item
-- @tab data Additional data for the item
-- @treturn Item The created item instance
function cityrp.item.Create(uniqueID, data)
```

**After:**
```lua
--- Creates a new item instance.
--- @param uniqueID string The unique ID of the item
--- @param data table Additional data for the item
--- @return Item The created item instance
function cityrp.item.Create(uniqueID, data)
```

### Example 3 - Class Definition:
**Before:**
```lua
--[[--
A class for manipulating the item system.
]]
-- @module cityrp.item

--- @class Item
-- @field uniqueID string The unique ID of the item
-- @field name string Display name
-- @field description string Item description
-- @field data table Custom data table
local ITEM = {}
```

**After:**
```lua
--[[--
A class for manipulating the item system.
]]
---@module cityrp.item

--- @class Item
--- @field uniqueID string The unique ID of the item
--- @field name string Display name
--- @field description string Item description
--- @field data table Custom data table
local ITEM = {}
```

## Requirements
- Add type annotations to all public functions and methods
- Document all function parameters and return values
- Use `--- @class` for table-based objects and metatables
- Add `--- @field` for all class properties
- Preserve existing correct documentation
- Never modify the actual code being documented
- Focus only on documentation format conversion

## Do / Don’t
Do:
- Use LuaLS tags exactly as specified
- Keep descriptions intact and precise
- Add `any` when type is unknown

Don’t:
- Change runtime code or behavior
- Remove correct existing annotations
- Collapse or expand comment blocks unnecessarily
