# CityRP Gamemode Refactoring Guide for Copilot

**Goal:** Refactor existing core gamemode systems (`gamemode/core/`) based on the user's request, improving code quality, performance, or maintainability while preserving functionality.

**Core Principles:**

1.  **Understand the System:** Before refactoring, thoroughly understand the purpose, scope, and interactions of the target system.
    *   Identify key files, functions, and data structures.
    *   Use search tools to find usages of relevant symbols (functions, variables, tables, metatables).
    *   Consult related documentation or comments if available.
    *   Reference the main Copilot instructions: [#file:../copilot-instructions.md](../copilot-instructions.md)

2.  **Preserve Functionality:** The primary goal is to restructure or improve the code *without* changing its external behavior.
    *   Ensure all existing features relying on the refactored code still work as expected.
    *   Pay close attention to function signatures, return values, and side effects.

3.  **Lua Specifics - Symbol Usage & Typing:**
    *   **Case Sensitivity:** Lua is **case-sensitive**. `myVariable` is different from `myvariable`. Verify all symbol names meticulously.
    *   **No Strict Typing:** Variables and table fields do not have fixed types. A variable might hold a number, then a string, then nil.
    *   **Check Usages:** Because <PERSON><PERSON> is not strictly typed, **it is crucial to check all usages of a function, variable, or table key before renaming or changing its behavior.** Use code search tools (`semantic_search`, `list_code_usages`, `grep_search`) extensively to find all references throughout the codebase (`gamemode/` and `plugins/`).
    *   **Metatables:** Be aware of metatables ([#file:../../gamemode/core/metatables/](../../gamemode/core/metatables/)) which add methods and behavior to standard Lua types (like Players, Entities, Items, Inventories). Changes to core libraries might require corresponding changes in metatables or vice-versa.

4.  **Leverage Core APIs:**
    *   When refactoring, look for opportunities to replace custom logic with existing core APIs (e.g., `cityrp.util`, `cityrp.item`, `cityrp.inventory`, `cityrp.net`). See [#file:../workflows/copilot-instructions.md](../copilot-instructions.md) Section 2 & 4.
    *   Consolidate redundant code into shared utility functions within `cityrp.util` ([#file:../../gamemode/core/sh_util.lua](../../gamemode/core/sh_util.lua)) if appropriate.

5.  **Maintain Style:** Adhere to the existing code style and patterns found within the `gamemode/core/` directory.

6.  **Testing (Conceptual):** While automated tests aren't present, think about edge cases and different scenarios where the refactored code will be used. Manually test the changes if possible or describe testing steps for the user.

**Workflow:**

1.  **Analyze Request:** Understand which system(s) the user wants to refactor and the specific goals (e.g., performance, readability).
2.  **Gather Context:** Use tools to read relevant files and find all usages of key symbols involved in the refactoring.
3.  **Plan Changes:** Outline the proposed changes, keeping backward compatibility and Lua's dynamic nature in mind.
4.  **Implement Changes:** Apply the refactoring incrementally.
5.  **Verify:** Double-check that all usages identified in step 2 have been updated correctly. Use `get_errors` to check for syntax issues.
6.  **Explain:** Clearly explain the changes made and why they were made.

**Task:** Based on the user's refactoring request and the principles above, carefully modify the specified core gamemode files. Prioritize understanding symbol usage across the codebase before making changes.
