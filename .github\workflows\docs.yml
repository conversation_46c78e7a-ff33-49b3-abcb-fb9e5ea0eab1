name: Documentation

on:
  push:
    paths:
      - '.github/workflows/docs.yml'
      - 'gamemode/**'
      - 'plugins/**'
      - 'docs/**'
      - 'config.ld'
    branches:
      - master
      - main

jobs:
  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          path: cityrp

      - uses: leafo/gh-actions-lua@v8.0.0
        with:
          luaVersion: "5.2"

      - uses: leafo/gh-actions-luarocks@v4.0.0

      - name: Pull LDoc
        uses: actions/checkout@v2
        with:
          repository: Pollux12/LDoc
          path: ldoc

      - name: Build LDoc
        working-directory: ldoc
        run: luarocks make

      - name: Build docs
        working-directory: cityrp
        run: ldoc .

      - name: Copy assets
        working-directory: cityrp
        run: |
          cp -v docs/css/* docs/html
          cp -v docs/js/* docs/html

      - name: Deploy
        if: github.event_name == 'push' && github.ref == 'refs/heads/master' && github.repository == 'FearlessGaming/CityRP' && success()
        uses: peaceiris/actions-gh-pages@v3
        with:
          personal_token: ${{ secrets.DOCS_TOKEN }}
          external_repository: FearlessGaming/CityRP-Documentation
          publish_branch: gh-pages
          publish_dir: cityrp/docs/html
          cname: docs.fearlessrp.net
