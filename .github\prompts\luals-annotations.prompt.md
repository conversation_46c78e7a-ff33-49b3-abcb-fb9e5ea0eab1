Act as a Lua code refactoring tool. Your goal is to convert all LDoc documentation comments to the LuaLS annotation format, matching the specific coding style of the CityRP gamemode.

Follow these rules:
1. Identify comment lines containing LDoc tags (starting with `@`).
2. For each LDoc tag found, replace the prefix and tag with the LuaLS equivalent (prefixed with `---`).
3. Preserve all content *after* the tag (like variable names, types, descriptions, etc.).
4. Focus on lines that clearly contain LDoc tags. Ignore regular comments.
5. Convert LDoc style parameter types (from `@tparam`, `@string`, etc.) to LuaLS format by rearranging parameter order.
6. Maintain the original code structure - don't condense or expand comment blocks.
7. Preserve the code being documented - only change the documentation format.
8. If a documentation block starts with a triple-dash (`---`) followed by plain text, keep that as is.
9. In CityRP, documentation often starts with a single `---` line for description, followed by multiple `--` lines for tags.
10. Do not touch existing LuaLS annotations or comments that are already correct and accurate.
11. Add annotations to any functions, methods or classes that are currently missing them, using the LuaLS format.
12. If any existing annotations are incorrect or missing detail, fix and/or update them to be accurate and in the LuaLS format.

## Parameter Type Conversions (Key Differences)

### LDoc Parameter Style vs LuaLS Style:
- LDoc: `-- @tparam <type> <name> <description>`
- LuaLS: `--- @param <name> <type> <description>`

### Type Parameter Format Differences:
- `@param` in LDoc doesn't specify type, but LuaLS requires it. Add `any` if type is missing.
- `@tparam` in LDoc has type before name, but LuaLS has type after name.
- Specialized type tags like `@string`, `@number`, etc. should convert to `@param` with appropriate type.

## Common Mappings:
- `-- @param name desc`  -> `--- @param name any desc`  (Add explicit `any` type)
- `-- @tparam string name desc` -> `--- @param name string desc`  (Reorder type after name)
- `-- @string name desc` -> `--- @param name string desc`  (Convert to param with string type)
- `-- @number name desc` -> `--- @param name number desc`  (Convert to param with number type)
- `-- @integer name desc` -> `--- @param name integer desc`  (Convert to param with integer type)
- `-- @bool name desc` -> `--- @param name boolean desc`  (Convert to param with boolean type)
- `-- @tab name desc` -> `--- @param name table desc`  (Convert to param with table type)
- `-- @array name desc` -> `--- @param name any[] desc`  (Convert to param with array type)
- `-- @func name desc` -> `--- @param name function desc`  (Convert to param with function type)
- `-- @entity name desc` -> `--- @param name Entity desc`  (Convert to param with Entity type)
- `-- @player name desc` -> `--- @param name Player desc`  (Convert to param with Player type)

- `-- @return desc` -> `--- @return any desc`  (Add explicit `any` type if missing)
- `-- @treturn string desc` -> `--- @return string desc`  (Keep type position)
- `-- @treturn[1]` -> `--- @return `  (Numbered return values)
- `-- @treturn[2]` -> `--- @return `  (Numbered return values)

- `-- @class Name` -> `--- @class Name`
- `-- @field name type desc` -> `--- @field name type desc`  (Keep parameter order)
- `-- @type type` -> `--- @type type`
- `-- @module name` -> `--- @module name`
- `-- @alias name` -> `--- @alias name`
- `-- @usage example` -> `--- @usage example`
- `-- @deprecated reason` -> `--- @deprecated reason`
- `-- @realm type` -> `--- @realm type`
- `-- @table name` -> `--- @class name`  (Usually defines a class-like table)
- `-- @see ref` -> `--- @see ref`
- `-- @internal` -> `--- @internal`
- `-- @generic T` -> `--- @generic T`
- `-- @operator` -> `--- @operator`
- `-- @vararg` -> `--- @vararg`
- `-- @meta` -> `--- @meta`
- `-- @async` -> `--- @async`
- `-- @enum` -> `--- @enum`
- `-- @function name` -> `--- @function name` (Declaration of function)
- `-- @overload fun(params):return` -> `--- @overload fun(params):return`

## Example Conversions from CityRP Style:

Example 1 - Single line description with params:
```lua
--- Does something cool with an item.
-- @param ply The player who triggered the action.
-- @tparam string itemID The ID of the item to use.
-- @return True on success, false otherwise.
function cityrp.UseItem(ply, itemID)
```

Becomes:
```lua
--- Does something cool with an item.
--- @param ply any The player who triggered the action.
--- @param itemID string The ID of the item to use.
--- @return boolean True on success, false otherwise.
function cityrp.UseItem(ply, itemID)
```

Example 2 - Type-specific parameter conversion:
```lua
--- Creates a new item instance.
-- @string uniqueID The unique ID of the item
-- @tab data Additional data for the item
-- @treturn Item The created item instance
function cityrp.item.Create(uniqueID, data)
```

Becomes:
```lua
--- Creates a new item instance.
--- @param uniqueID string The unique ID of the item
--- @param data table Additional data for the item
--- @return Item The created item instance
function cityrp.item.Create(uniqueID, data)
```

Example 3 - Class definition format used in CityRP:
```lua
--[[--
A class for manipulating the item system.
]]
-- @module cityrp.item

--- @class Item
-- @field uniqueID string The unique ID of the item
-- @field name string Display name
-- @field description string Item description
-- @field data table Custom data table
local ITEM = {}
```

Becomes:
```lua
--[[--
A class for manipulating the item system.
]]
---@module cityrp.item

--- @class Item
--- @field uniqueID string The unique ID of the item
--- @field name string Display name
--- @field description string Item description
--- @field data table Custom data table
local ITEM = {}
```

Apply this conversion to the provided Lua code while preserving the original code structure and documentation content.