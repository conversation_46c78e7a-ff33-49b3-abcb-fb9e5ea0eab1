// renault_alpine, created by [FL] Conn in Fri Jun 24 16:44:26 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1.1"
		"MassCenterOverride"	"0 2 8"
		"MassOverride"			"2200"
		"AddGravity"			"0.3"
		"MaxAngularVelocity"	"1000"
	}
	"Engine"
	{
		"HorsePower"			"260"
		"MaxRPM"				"5000"
		"MaxSpeed"				"65"
		"MaxReverseSpeed"		"20"
		"AutobrakeSpeedGain"	"0"
		"AutobrakeSpeedFactor"	"0"
		"Autotransmission"		"0"
		"AxleRatio"				"8"
		"Gear"					"6"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"4100"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"11"
		"DegreesBoost"						"11"
		"FastDampen"						"100"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"11"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"2.7"
		"FastSteeringRate"					"1.8"
		"SteeringRestRateSlow"				"2.7"
		"SteeringRestRateFast"				"1.8"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.65"
		"BrakeSteeringRateFactor"			"2.65"
		"ThrottleSteeringRestRateFactor"	"1.75"
		"BoostSteeringRestRateFactor"		"1.5"
		"BoostSteeringRateFactor"			"1.5"

		"PowerSlideAccel"					"180"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"14.2"
			"Mass"							"150"
			"Inertia"						"2"
			"Damping"						"0.85"
			"RotDamping"					"0.85"
			"Material"						"phx_rubbertire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"90"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"8"
			"MaxBodyForce"					"13"
		}
		"TorqueFactor"						"0.44"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"14.2"
			"Mass"							"150"
			"Inertia"						"2"
			"Damping"						"0.85"
			"RotDamping"					"0.85"
			"Material"						"phx_rubbertire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"100"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"8"
			"MaxBodyForce"					"13"
		}
		"TorqueFactor"						"0.4"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.1"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.2"
		"Speed_Approach_Factor"	"0.052"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.034"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.033"
	}
	"Gear"
	{
		"Max_Speed"				"2"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/lwcars/renault_alpine/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/lwcars/renault_alpine/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/lwcars/renault_alpine/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/lwcars/renault_alpine/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/lwcars/renault_alpine/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/lwcars/renault_alpine/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/lwcars/renault_alpine/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/lwcars/renault_alpine/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/lwcars/renault_alpine/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/lwcars/renault_alpine/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/lwcars/renault_alpine/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/lwcars/renault_alpine/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/lwcars/renault_alpine/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/lwcars/renault_alpine/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}
