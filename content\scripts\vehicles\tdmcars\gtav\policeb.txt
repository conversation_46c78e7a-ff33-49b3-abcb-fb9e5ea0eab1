// policeb, created by [<PERSON>] gin in Tue Jun 28 23:28:22 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1.3"
		"MassCenterOverride"	"0 0 10"
		"MassOverride"			"700"
		"AddGravity"			"1.2"
		"MaxAngularVelocity"	"300"
	}
	"Engine"
	{
		"HorsePower"			"78"
		"MaxRPM"				"5000"
		"MaxSpeed"				"77"
		"MaxReverseSpeed"		"3"
		"AutobrakeSpeedGain"	"0"
		"AutobrakeSpeedFactor"	"0"
		"Autotransmission"		"0"
		"AxleRatio"				"6"
		"Gear"					"7"
		"Gear"					"2.4"
		"Gear"					"1.5"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"3500"
	}
	"Steering"
	{
		"DegreesSlow"						"22"
		"DegreesFast"						"3"
		"DegreesBoost"						"11"
		"FastDampen"						"79"
		"SteeringExponent"					"0.96"
		"SlowCarSpeed"						"4"
		"FastCarSpeed"						"48"
		"SlowSteeringRate"					"2.69"
		"FastSteeringRate"					"2.19"
		"SteeringRestRateSlow"				"2.53"
		"SteeringRestRateFast"				"1.34"
		"TurnThrottleReduceSlow"			"0.09"
		"TurnThrottleReduceFast"			"0.62"
		"BrakeSteeringRateFactor"			"1.5"
		"ThrottleSteeringRestRateFactor"	"1"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"180"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16"
			"Mass"							"75"
			"Inertia"						"2"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"162"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7.44"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.1"
		"BrakeFactor"						"0.57"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16"
			"Mass"							"75"
			"Inertia"						"2"
			"Damping"						"0.25"
			"RotDamping"					"1"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"280"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7"
			"MaxBodyForce"					"78"
		}
		"TorqueFactor"						"1"
		"BrakeFactor"						"0.68"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.06"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.16"
		"Speed_Approach_Factor"	"0.08"
	}
	"Gear"
	{
		"Max_Speed"				"0.28"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.54"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.01"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\tdmcars\gtav\policeb\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\tdmcars\gtav\policeb\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\tdmcars\gtav\policeb\idle.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\tdmcars\gtav\policeb\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\tdmcars\gtav\policeb\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\tdmcars\gtav\policeb\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\tdmcars\gtav\policeb\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\tdmcars\gtav\policeb\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\tdmcars\gtav\engineoff.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\tdmcars\gtav\startup.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\tdmcars\gtav\policeb\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\tdmcars\gtav\policeb\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\tdmcars\gtav\policeb\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\tdmcars\gtav\policeb\idle.mp3"
		"Min_Time"	"0.08"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\tdmcars\gtav\policeb\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\tdmcars\gtav\policeb\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}
