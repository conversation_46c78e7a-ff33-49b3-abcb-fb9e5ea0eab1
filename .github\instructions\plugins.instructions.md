---
applyTo: "plugins/**"
---

# Plugin Development Instructions

Scope: Author, refactor, and maintain isolated plugins that integrate via core `cityrp.*` APIs without modifying core or other plugins.

When to use: Any change within `plugins/<plugin_name>/` including structure, hooks, items, UI, entities, and migrations.

## Plugin Isolation Rules
- Work ONLY within `plugins/<plugin_name>/` directory
- Never modify core files (`gamemode/`) or other plugins
- Use established `cityrp.*` APIs for core functionality
- Never define `GM:` hook functions - use `PLUGIN:` or `hook.Add()` instead

## Plugin Structure Template
**Entry Point:** `sh_plugin.lua` with complete PLUGIN table:
```lua
PLUGIN.name = "Plugin Name"
PLUGIN.author = "Author Name"
PLUGIN.description = "Detailed plugin description"

-- Initialize runtime data storage
PLUGIN.exampleData = {}

-- Called after plugin and auto-included files are loaded
function PLUGIN:OnLoaded()
    self:Logger():Info("Plugin loaded!")
    self:LoadData()
end

-- Called periodically to save plugin data
function PLUGIN:SaveData()
    self.exampleData = self.exampleData or {}
    self.exampleData.someCounter = (self.exampleData.someCounter or 0) + 1
    self:SetData(self.exampleData)
    self:Logger():Debug("Data saved.")
end

-- Called after map load to load plugin data
function PLUGIN:LoadData()
    self.exampleData = self:GetData({})
    self:Logger():Info("Data loaded. Counter:", self.exampleData.someCounter or 0)
end

-- Example hook - auto-registered by name
function PLUGIN:PlayerInitialSpawn(ply)
    -- Add player spawn logic here
end
```

## Auto-Loading Directories (load order)
1. `languages/` - Translation files (e.g., `en.lua`)
2. `libs/` - Shared library code and helper functions
3. `items/` - Plugin-specific item definitions
4. `plugins/` - Nested plugins loaded by this plugin
5. `derma/` - VGUI/Derma UI elements
6. `entities/` - Custom entities, weapons, effects, tools
7. `itemspostload/` - Code needing all items loaded first
8. `migrations/` - Database schema changes (e.g., `sv_timestamp_name.lua`)

## Coding Patterns
**Plugin Functions:**
- Use `PLUGIN:FunctionName()` for plugin-scoped functions and hooks
- Auto-registered as hooks if `FunctionName` matches GMod hook names
- Access plugin logger with `self:Logger()`
- All PLUGIN functions are hooks when defined anywhere in the plugin

**Standard Hooks:**
Use `hook.Add()` when:
- Need unique key to prevent duplicate hooks
- Splitting large hook into multiple smaller functions
- Hooking custom/non-standard hooks
- Want explicit control over hook registration

**Data Persistence:**
- `self:SetData(table)` - Save plugin runtime data
- `self:GetData(defaultTable)` - Load plugin saved data
- Called automatically by core system for data management

## Core API Reference
**Essential APIs:**
- `cityrp.util` - File includes, JSON, helpers (`cityrp.util.Include()`)
- `cityrp.net` - Entity state networking (prefer over `SetNW*`)
- `mysql` - Database queries with builder pattern
- `cityrp.logging` - Structured logging system
- `cityrp.commandNew` - Modern command registration
- `cityrp.item`/`cityrp.inventory` - Item and inventory systems

**Database Usage:**
- Schema changes: Create files in plugin `migrations/` folder
- Queries: Use `mysql:Select().Where()...Execute()` builder pattern
- Reference core migrations as examples, but keep plugin-specific

**UI Development:**
- Place Derma panels in plugin `derma/` folder
- Use core skin: reference `gamemode/core/cl_skinbeta.lua`
- Follow existing UI patterns and styling

## Best Practices
- **Verify Symbol Names:** Lua is case-sensitive - double-check all references
- **Use Local Functions:** Avoid globals when function doesn't need external access
- **Match Existing Style:** Follow patterns in surrounding code
- **Leverage Existing Code:** Search core libraries before writing new functions
- **Test Isolation:** Ensure plugin works independently and doesn't break others

## Do / Don’t
Do:
- Define hooks as `PLUGIN:HookName(...)` to enable auto-registration
- Use `hook.Add` when unique keys or multiple handlers are needed
- Store and persist state via `self:GetData()`/`self:SetData()`
- Follow auto-loading directory order for predictable behavior

Don’t:
- Modify files in `gamemode/` or other plugins
- Define `GM:` hooks in plugins
- Call other plugins directly (use hooks or core APIs)
- Use direct SQL; always use `mysql` builder
