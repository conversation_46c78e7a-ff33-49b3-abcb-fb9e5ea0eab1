# CityRP Item Creation/Editing Guide for Copilot

**Goal:** Create a new CityRP item definition or item base, or modify an existing one. Ensure the use of the modern item system (`ITEM:New()`, `:AddAction()`, `:DisableAutoActions()`) and update legacy items where applicable, following the conventions set by `sh_base_equip.lua`.

**Information Gathering:**
*   **Type:** Is the request for an **item base** (defining behavior for a *type* of item) or an **item definition** (a specific item inheriting from a base)?
*   **New Item/Base:** Ask for its name, the base it should inherit from (if applicable, e.g., `base_item`, `base_equip`), and the intended location (core or specific plugin).
*   **Existing Item/Base:** Identify the file path (could be in `gamemode/core/items/`, `gamemode/core/libs/`, or a plugin's `items/` or `libs/` directory).

**Requirements & Constraints:**

1.  **Placement Strategy:**
    *   **Item Definitions:**
        *   Plugin-Specific: Place in the plugin's `items/` directory (e.g., `plugins/<plugin_name>/items/sh_myitem.lua`). Loaded automatically by [#file:../../gamemode/core/libraries/sh_plugin.lua](../../gamemode/core/libraries/sh_plugin.lua).
        *   Core/Shared: Place in `gamemode/core/items/` (e.g., `gamemode/core/items/other/sh_core_item.lua`).
    *   **Item Bases:**
        *   Core/Widely Used: Place in `gamemode/core/items/base/` or `gamemode/core/items/postbase/` (e.g., `gamemode/core/items/base/sh_base_mybasetype.lua`). Needed for load order if other core items or multiple plugins depend on it.
        *   Plugin-Specific: Place in the plugin's `libs/` directory (e.g., `plugins/<plugin_name>/libs/sh_myplugin_base.lua`). Loaded automatically by [#file:../../gamemode/core/libraries/sh_plugin.lua](../../gamemode/core/libraries/sh_plugin.lua).
    *   **Isolation Principle:** When working on a plugin's item/base, primarily modify files within that plugin's directory unless creating/editing a shared base in core.
    *   Reference: [#file:plugin-creation.prompt.md](plugin-creation.prompt.md) Section 1 & 3.

2.  **Modern Item System (`ITEM:New()`):**
    *   **Foundation:** Use the `ITEM:New("base_name")` system defined in [#file:../../gamemode/core/metatables/sh_base_item.lua](../../gamemode/core/metatables/sh_base_item.lua). All items and bases start by inheriting from `base_item` (implicitly or explicitly).
    *   **Avoid Legacy Systems:** Do **NOT** use older methods (direct `cityrp.item.list` manipulation, `sh_itemcore.lua` structures, defining `OnEquip`, `OnHolster`, `canAction`, `onUse`, `onDrop`, `onGive`, etc. directly on the item table). Focus *only* on `ITEM:New()` and the Action Builder (`:AddAction`).
    *   **Refactor Legacy:** If editing a legacy item/base, **update it** to the modern `ITEM:New()` structure, replacing direct function overrides with the Action Builder. **This includes updating the base first, and then any items inheriting from that base if their usage needs to change.**

3.  **Item Bases (Defining Behavior):**
    *   **Purpose:** Define *how* a category of items functions (shared logic, actions, default properties).
    *   **Inheritance:** Bases inherit from other bases (ultimately `base_item`). Example: `base_swep` inherits from `base_equip`.
    *   **Structure:**
        *   Start with `ITEM:New("parent_base_name")`.
        *   **Crucially, call `:DisableAutoActions()` immediately after `ITEM:New(...)`** to prevent conflicts with the legacy action system. Example: `ITEM:New("base_item"):DisableAutoActions()`.
        *   Use methods like `:SetDescription()`, `:SetCategory()`, `:SetDefaultPlayerModel()`, etc., to define default properties for items inheriting this base.
        *   **Define ALL behavior and interactions using the Action Builder:** Use the action builder pattern with `:AddAction("ActionName"):SetCondition():SetAction():Build()` for every way a player can interact with the item (e.g., Equip, Use, Drop, Give, Place, Custom Actions). **Do NOT define `OnEquip`, `canAction`, `onUse`, etc. directly.**
            *   Example (`base_equip`): [#file:../../gamemode/core/items/base/sh_base_equip.lua](../../gamemode/core/items/base/sh_base_equip.lua) shows `Equip`, `Unequip`, `Drop`, `Give` actions defined using the action builder pattern. **Use this file as the primary template/example for modern bases.**
            *   Action builder pattern:
                *   Start with `:AddAction("ActionName")`
                *   Use `:SetName("Display Name")` to set the display name (optional, defaults to action ID)
                *   Use `:SetIcon("icon16/example.png")` to set the UI icon (optional)
                *   Use `:SetCondition(functionReference)` to set validation function - **Convention:** Name this function `ActionNameCondition` (e.g., `EquipCondition`). **Crucial for validation.**
                *   Use `:SetAction(functionReference)` to set action logic function - **Convention:** Name this function `ActionNameAction` (e.g., `EquipAction`).
                *   Use `:SetSortRank(number)` to control action ordering (optional, lower numbers appear first)
                *   End with `:Build()` to register the action
            *   Function signatures:
                *   `condition(base, item, client, ...)`: Returns `true` if action is allowed, or `false, "reason"` if not
                *   `action(base, item, client, ...)`: Performs the action logic (server-side)
            *   Reference Action Builder: [#file:../../gamemode/core/metatables/sh_base_item.lua](../../gamemode/core/metatables/sh_base_item.lua) (`:AddAction` method).
    *   **Base Hooks & Methods:** Define these directly on the `ITEM` table within the base file:
        *   **Lifecycle Hooks:**
            *   `ITEM:PlayerSpawnHook(ply, isInitial)`: Called when a player holding this item spawns.
            *   `ITEM:PlayerDeathHook(ply, attacker, dmgInfo)`: Called when a player holding this item dies.
            *   `ITEM:ItemRegistered()`: Called once when the item base/definition is first registered.
            *   `ITEM:ItemCreated(itemInstance)`: Called when a new instance of this item is created in an inventory.
        *   **Inventory UI Hooks:**
            *   `ITEM:PaintOver(itemInstance, x, y, w, h)`: (Client-side) Allows drawing custom elements over the item icon in the inventory.
            *   `ITEM:PopulateTooltip(tooltip, itemInstance)`: (Client-side) Allows adding custom lines or modifying the item's tooltip when hovered in the inventory.
    *   **Example Modern Base:**
        *   Equipable Base: [#file:../../gamemode/core/items/base/sh_base_equip.lua](../../gamemode/core/items/base/sh_base_equip.lua) (**Primary Example**)

4.  **Item Definitions (Specific Instances):**
    *   **Purpose:** Define a specific, usable item that players interact with. Inherits *all* actions and behaviors from its base.
    *   **Inheritance:** *Must* inherit from an existing **item base** using `ITEM:New("base_name")`.
    *   **Structure:**
        *   Keep definitions minimal. Focus on setting unique properties using `:Set...` methods.
        *   Use methods like `:SetName()`, `:SetDescription()`, `:SetModel()`, `:SetCost()`, `:SetSize()`, `:SetStackable(maxStack)`, etc.
        *   **Do NOT** call `:DisableAutoActions()` here (it's done in the base).
        *   **Do NOT** use `:AddAction` here. Actions are inherited from the base.
        *   **Do NOT** define base hooks (`PlayerSpawnHook`, `PaintOver`, etc.) here unless absolutely necessary to override base behavior for *only this specific item*. Logic should primarily reside in the base.
        *   Override base methods (like `:GetDescription` if needed for dynamic descriptions) *only if* this specific item needs unique non-action behavior. Avoid overriding action-related logic.
    *   **Example Modern Item Definition:**
        *   Equip Test Item: [#file:../../gamemode/core/items/other/sh_equip_test.lua](../../gamemode/core/items/other/sh_equip_test.lua) (inherits `base_equip`) (**Primary Example**)
        *   SWEP Test Item: [#file:../../gamemode/core/items/other/sh_swep_test.lua](../../gamemode/core/items/other/sh_swep_test.lua) (inherits `base_swep`)

5.  **Core API Usage:**
    *   Utilize relevant `cityrp.*` APIs (e.g., `cityrp.util`, `cityrp.inventory`).
    *   **Item Instance Methods:** Interact with items *in inventories* using methods from the `Item` metatable: [#file:../../gamemode/core/metatables/sh_item.lua](../../gamemode/core/metatables/sh_item.lua) (e.g., `item:GetOwner()`, `item:GetData()`, `item:SetData()`, `item:GetStack()`, `item:Delete()`). **Note:** These are for *instances* of items, not definitions/bases.
        *   To trigger an action defined by `AddAction` from the client, use `item:RequestAction("ActionName", data_table)`. The server handles routing this to the correct `action` function defined in the base.
        *   Avoid calling legacy server-side functions like `item:PlayerUse`, `item:PlayerDrop` directly. Use `item:RequestAction` from client or trigger the action logic internally on the server if needed, respecting the `condition` function.
    *   **Inventory Instance Methods:** Interact with inventories using methods from the `Inventory` metatable: [#file:../../gamemode/core/metatables/sh_inventory.lua](../../gamemode/core/metatables/sh_inventory.lua) (e.g., `inv:AddItem()`, `inv:RemoveItem()`, `inv:CanFitItem()`).
    *   Reference: [#file:../workflows/copilot-instructions.md](../copilot-instructions.md) Sections 2 & 4.

6.  **Coding Conventions:**
    *   Follow Lua best practices (case-sensitive, check for nil).
    *   Maintain the fluent style (`ITEM:New(...):DisableAutoActions():Set...():AddAction()...`) for bases.
    *   Maintain the fluent style (`ITEM:New(...):Set...():Set...()`) for definitions.
    *   Match the style of `sh_base_equip.lua` for bases and `sh_equip_test.lua` for definitions.
    *   Use the `ActionNameCondition` / `ActionNameAction` naming convention for action functions within `:AddAction`.

**Task:** Based on the user's request:
*   **Create:** Generate the necessary Lua file(s) (item definition or base) in the appropriate location (core or plugin `items/`/`libs/`) using the modern `ITEM:New()`, `:DisableAutoActions()` (for bases), and `:AddAction()` (for bases) system, following all conventions.
*   **Edit:** Modify the existing item/base file. If it uses a legacy system (direct function overrides like `OnEquip`, `canAction`, etc.), refactor it to the modern system using `:DisableAutoActions()` and the Action Builder (`:AddAction`), following all conventions. Ensure logic resides primarily in the base class actions. If refactoring a base, check if inheriting items or inheriting bases also need updates.
