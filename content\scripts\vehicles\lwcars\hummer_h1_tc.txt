"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"		"2"
		"MassCenterOverride"		"0 3 10"
		"MassOverride"				"5000"
		"AddGravity"				"0.5"
		"MaxAngularVelocity"		"900"
	}
	"Engine"
	{
		"HorsePower"				"720"
		"MaxRPM"					"4500"
		"MaxSpeed"					"60"
		"MaxReverseSpeed"			"25"
		"AutobrakeSpeedGain"		"0.9"
		"AutobrakeSpeedFactor"		"0.5"
		"Autotransmission"			"0"
		"AxleRatio"					"5"

		"Gear"						"2.7"


		"ShiftUpRPM"				"2900"
		"ShiftDownRPM"				"1600"
	}
	"Steering"
	{
		"DegreesSlow"							"40"
		"DegreesFast"							"21"
		"DegreesBoost"							"11"
		"FastDampen"							"0"
		"SteeringExponent"						"1.4"
		"SlowCarSpeed"							"14"
		"FastCarSpeed"							"35"
		"SlowSteeringRate"						"3"
		"FastSteeringRate"						"1.6"
		"SteeringRestRateSlow"					"3"
		"SteeringRestRateFast"					"1.6"
		"TurnThrottleReduceSlow"				"0.01"
		"TurnThrottleReduceFast"				"0.5"
		"BrakeSteeringRateFactor"				"3"
		"ThrottleSteeringRestRateFactor"		"1.6"
		"BoostSteeringRestRateFactor"			"1.7"
		"BoostSteeringRateFactor"				"1.7"

		"PowerSlideAccel"						"70"

		"SkidAllowed"							"1"
		"DustCloud"								"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"20.5"
			"Mass"							"390"
			"Inertia"						"0"
			"Damping"						"0.9"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"30"
			"SpringDamping"					"0.3"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"3"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"1.5"
		"BrakeFactor"						"0.3"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"20.5"
			"Mass"							"390"
			"Inertia"						"0"
			"Damping"						"0.9"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"45"
			"SpringDamping"					"0.3"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"3"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"1.35"
		"BrakeFactor"						"0.6"
	}
}


"Vehicle_Sounds"
{
        "Gear"
        {
                "Max_Speed"                             "0.02"
                "Speed_Approach_Factor" "1"
        }
        "Gear"
        {
                "Max_Speed"                             "0.2"
                "Speed_Approach_Factor" "0.05"
        }
        "Gear"
        {
                "Max_Speed"                             "0.38"
                "Speed_Approach_Factor" "0.052"
        }
        "Gear"
        {
                "Max_Speed"                             "0.49"
                "Speed_Approach_Factor" "0.034"
        }
        "Gear"
        {
                "Max_Speed"                             "1.5"
                "Speed_Approach_Factor" "0.033"
        }
        "Gear"
        {
                "Max_Speed"                             "2"
                "Speed_Approach_Factor" "0.03"
        }
        "State"
        {
                "Name"          "SS_GEAR_2_RESUME"
                "Sound"         "vehicles/lwcars/hummer_h1/second.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_GEAR_3_RESUME"
                "Sound"         "vehicles/lwcars/hummer_h1/third.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_REVERSE"
                "Sound"         "vehicles/lwcars/hummer_h1/rev.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_IDLE"
                "Sound"         "vehicles/lwcars/hummer_h1/idle.mp3"
                "Min_Time"      "0"
        }
        "State"
        {
                "Name"          "SS_GEAR_1_RESUME"
                "Sound"         "vehicles/lwcars/hummer_h1/first.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_GEAR_4"
                "Sound"         "vehicles/lwcars/hummer_h1/fourth_cruise.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_GEAR_1"
                "Sound"         "vehicles/lwcars/hummer_h1/first.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_SLOWDOWN_HIGHSPEED"
                "Sound"         "vehicles/lwcars/hummer_h1/throttle_off_fast.mp3"
                "Min_Time"      "2"
        }
        "State"
        {
                "Name"          "SS_START_IDLE"
                "Sound"         "vehicles/lwcars/hummer_h1/start.mp3"
                "Min_Time"      "4"
        }
        "State"
        {
                "Name"          "SS_GEAR_4_RESUME"
                "Sound"         "vehicles/lwcars/hummer_h1/fourth_cruise.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_GEAR_3"
                "Sound"         "vehicles/lwcars/hummer_h1/third.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_GEAR_0_RESUME"
                "Sound"         "vehicles/lwcars/hummer_h1/idle.mp3"
                "Min_Time"      "0.05"
        }
        "State"
        {
                "Name"          "SS_GEAR_0"
                "Sound"         "vehicles/lwcars/hummer_h1/rev.mp3"
                "Min_Time"      "0.05"
        }
        "State"
        {
                "Name"          "SS_GEAR_2"
                "Sound"         "vehicles/lwcars/hummer_h1/second.mp3"
                "Min_Time"      "0.5"
        }
        "State"
        {
                "Name"          "SS_SLOWDOWN"
                "Sound"         "vehicles/lwcars/hummer_h1/idle.mp3"
                "Min_Time"      "2"
        }
        "CrashSound"
        {
                "Min_Speed"                     "350"
                "Min_Speed_Change"      "250"
                "Sound"                         "atv_impact_medium"
                "Gear_Limit"            "1"
        }
        "CrashSound"
        {
                "Min_Speed"                     "450"
                "Min_Speed_Change"      "350"
                "Sound"                         "atv_impact_heavy"
                "Gear_Limit"            "0"
        }
 
        "Skid_LowFriction"              "atv_skid_lowfriction"
        "Skid_NormalFriction"   "atv_skid_normalfriction"
        "Skid_HighFriction"             "atv_skid_highfriction"
    }