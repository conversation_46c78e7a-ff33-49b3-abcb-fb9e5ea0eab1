// volvo_fh16, created by TDM in 05/25/15 20:16:27, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 -20 25"
		"MassOverride"			"9000"
		"AddGravity"			"0.7"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"750"
		"MaxRPM"				"4700"
		"MaxSpeed"				"70"
		"MaxReverseSpeed"		"12"
		"AutobrakeSpeedGain"	"0"
		"AutobrakeSpeedFactor"	"0"
		"Autotransmission"		"0"
		"AxleRatio"				"7.6"
		"Gear"					"3.2"
		"Gear"					"2.4"
		"Gear"					"1.5"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"6"
		"DegreesBoost"						"11"
		"FastDampen"						"90"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"3"
		"FastCarSpeed"						"45"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"2.5"
		"SteeringRestRateFast"				"1.8"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"0.8"
		"ThrottleSteeringRestRateFactor"	"1.8"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"20"
			"Mass"							"800"
			"Inertia"						"0"
			"Damping"						"0.1"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"55"
			"SpringDamping"					"0.9"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"340"
		}
		"TorqueFactor"						"0"
		"BrakeFactor"						"0.4"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"19"
			"Mass"							"1000"
			"Inertia"						"-3"
			"Damping"						"1"
			"RotDamping"					"1"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"90"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5.5"
			"MaxBodyForce"					"1800"
		}
		"TorqueFactor"						"2"
		"BrakeFactor"						"0.6"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.25"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.8"
		"Speed_Approach_Factor"	"0.108"
	}
	"Gear"
	{
		"Max_Speed"				"0.98"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1.16"
		"Speed_Approach_Factor"	"0.015"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\tdmcars\vol_fh16\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"vehicles\tdmcars\vol_fh16\engineoff.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\tdmcars\vol_fh16\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\tdmcars\vol_fh16\rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\tdmcars\vol_fh16\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\tdmcars\vol_fh16\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\tdmcars\vol_fh16\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\tdmcars\vol_fh16\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\tdmcars\vol_fh16\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles\tdmcars\vol_fh16\engineoff.mp3"
		"Min_Time"	"1"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\tdmcars\vol_fh16\engineon.mp3"
		"Min_Time"	"1.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\tdmcars\vol_fh16\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\tdmcars\vol_fh16\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\tdmcars\vol_fh16\idle.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\tdmcars\vol_fh16\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\tdmcars\vol_fh16\second.mp3"
		"Min_Time"	"1.25"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\tdmcars\vol_fh16\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}
