// audi_r8, created by [FL] Conn in Fri Jun 24 16:42:16 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.85"
		"MassCenterOverride"	"0 -2 2"
		"MassOverride"			"1400"
		"AddGravity"			"1.2"
		"MaxAngularVelocity"	"100"
	}
	"Engine"
	{
		"HorsePower"			"450"
		"MaxRPM"				"4500"
		"MaxSpeed"				"90"
		"MaxReverseSpeed"		"30"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"6"
		"Gear"					"6.4"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"1100"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"9"
		"DegreesBoost"						"11"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"15"
		"FastCarSpeed"						"45"
		"SlowSteeringRate"					"3"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"4"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.45"
		"BrakeSteeringRateFactor"			"2.2"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"450"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"15.4"
			"Mass"							"350"
			"Inertia"						"0"
			"Damping"						"0.2"
			"RotDamping"					"0.5"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"105"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"9"
			"MaxBodyForce"					"30"
		}
		"TorqueFactor"						"0.3"
		"BrakeFactor"						"0.9"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16.4"
			"Mass"							"350"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0.8"
			"Material"						"jeeptire"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"130"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7"
			"MaxBodyForce"					"30"
		}
		"TorqueFactor"						"0.32"
		"BrakeFactor"						"0.95"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.15"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.25"
		"Speed_Approach_Factor"	"0.052"
	}
	"Gear"
	{
		"Max_Speed"				"0.36"
		"Speed_Approach_Factor"	"0.034"
	}
	"Gear"
	{
		"Max_Speed"				"0.6"
		"Speed_Approach_Factor"	"0.033"
	}
	"Gear"
	{
		"Max_Speed"				"0.8"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/skyautos/audiv10/second.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/skyautos/audiv10/third.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/skyautos/audiv10/rev.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/skyautos/audiv10/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/skyautos/audiv10/first.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/skyautos/audiv10/fourth_cruise.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/skyautos/audiv10/first.mp3"
		"Min_Time"	"0.3"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/skyautos/audiv10/slowdown.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/skyautos/audiv10/startup.mp3"
		"Min_Time"	"3"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/skyautos/audiv10/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/skyautos/audiv10/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/skyautos/audiv10/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/skyautos/audiv10/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/skyautos/audiv10/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/skyautos/audiv10/slowdown.mp3"
		"Min_Time"	"2"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
