--[[-- Shared item and item category library.
@copyright Fearless Gaming
<AUTHOR> Team
<AUTHOR> Gaming
@module cityrp.item
--]]
--
local logger = cityrp.logging:GetLogger("ItemCore")
local function hl(...)
	return logger:Highlight(...)
end

local precache

if SERVER then
	precache = not cityrpserver["DisablePrecache"]

	if not precache then
		print("Precaching items disabled")
	end
else
	-- TODO: make true later or make a shared config/network it or something
	--precache = !game.IsDedicated()
	precache = false
end

cityrp.item = cityrp.item or {}
cityrp.item.stored = cityrp.item.stored or {}
cityrp.item.cats = cityrp.item.cats or {}
cityrp.item.catIndex = 1

cityrp.util.Include("cityrp/gamemode/core/metatables/sh_base_item.lua")

--- Create a new item based on the given base uniqueID.
-- @string[opt] base Base Item UniqueID
-- @treturn ITEM Newly created item.
--- @generic T : base_item
--- @param base `T` # UniqueID of the item base to use
--- @return T | base_item # Created item class
function cityrp.item.new(base)
	if not base then
		if not ITEM then
			logger:Fatal("Attempted to create a new item, but the ITEM metatable was not found!")
			error("")
		end

		if ITEM then
			return setmetatable({ actions = {} }, ITEM)
		end
	end

	if cityrp.item.stored[base] then
		return setmetatable({ base = base, actions = table.copy(cityrp.item.stored[base].actions or {}) },
			{
				__index = cityrp.item.stored[base],
			})
	else
		logger:Fatal("Attempted to create item, but ITEM[" .. tostring(base) .. "] was not found!")
		error("")
	end
end

-- Make sure there's no invalid functions that should no longer exist.
local invalidItemFunctions = {
	["Sell"] = true,
	["Destroy"] = true,
}

--- Register a new item.
-- @tab item The item table to register.
-- @string[opt] base The uniqueID of the base item to take properties from.
function cityrp.item.register(item)

	-- Localise names.
	local name = item.name
	if name then
		local args = (item.localisationArgs or {}).name or {}
		local key
		if name == "@" then
			key = "item." .. item.uniqueID .. ".name"
		elseif name:StartWith("@") then
			key = name:sub(1)
		end

		if key then
			name = L(key, nil, unpack(args))
			logger:Trace2("Localising ", hl(key), " to ", hl(name))
		end
	end
	item.name = name

	local str = { "Registering ", hl(item.name or "UNKNOWN"), " (", hl(item.uniqueID), ")" }
	if item.base then
		table.insert(str, " from ")
		table.insert(str, hl(item.base))
	end
	logger:Debug(str)

	-- Handle model precaching.
	if precache then
		if item.storemodel then
			logger:Trace1("Precaching model ", hl(item.storemodel))
			item.storemodel = Model(item.storemodel)
		end
		if item.model then
			logger:Trace1("Precaching model ", hl(item.model))
			item.model = Model(item.model)
		end
	end

	local plural = item.plural
	if plural then
		local args = (item.localisationArgs or {}).plural or {}
		local key
		if plural == "@" then
			key = "item." .. item.uniqueID .. ".plural"
		elseif plural:StartWith("@") then
			key = plural:sub(1)
		end

		if key then
			plural = L(key, nil, unpack(args))
			logger:Trace2("Localising ", hl(key), " to ", hl(plural))
		end
	end
	item.plural = plural

	local description = item.description
	if description then
		local args = (item.localisationArgs or {}).desc or {}
		local key
		if description == "@" then
			key = "item." .. item.uniqueID .. ".desc"
		elseif description:StartWith("@") then
			key = description:sub(1)
		end

		if key then
			description = L(key, nil, unpack(args))
			logger:Trace2("Localising ", hl(key), " to ", hl(description))
		end
	end
	item.description = description

	--[[ This is just for the default stackable type, going to try minimise stacking items so I'm disabling it.
	if item.stackable != false then
		item.stackable = true
	end --]]

	-- Set a default for maxStack incase it was forgotten
	if item.stackable and item.maxstack == nil then
		item.maxstack = item.batch or 1
	end

	-- This ensures that all onX functions have corrisponding canX functions.
	-- However, as we haven't refactored the item commands, this doesn't really matter.
	-- for _, func in ipairs(itmFuncs) do
	-- 	if item["on" .. func] and not item["can" .. func] then
	-- 		item["can" .. func] = tFunc
	-- 		str = str .. ", applied override for can" .. func
	-- 	end
	-- end

	item.actions = item.actions or {}

	if not item.noAutoAction then

		for funcName, func in pairs(item) do
			funcName = tostring(funcName)
			if isfunction(func) and string.StartsWith(tostring(funcName), "on") then
				local actionName = string.sub(tostring(funcName), 3)
				if invalidItemFunctions[actionName] then
					logger:Warning("Item ", hl(item.name), " has a legacy function ", hl(funcName),
						" which should be removed!")
					continue
				end
				if item["can" .. actionName] and isfunction(item["can" .. actionName]) then
					item.actions[actionName] = {
						["name"] = actionName,
						["action"] = func,
						["condition"] = item["can" .. actionName],
					}
				else
					item.actions[actionName] = {
						["name"] = actionName,
						["action"] = func,
						["condition"] = function() return true end,
					}
				end
			end
		end

		-- Some idiot decided that anything that can drop should just also be able to give, because they're definitely the same thing and definitely don't need different checks or functions or anything. So here's a hacky fix for that, we add Give if it has Drop but not Give.
		if item.onDrop ~= nil and item.onGive == nil then
			item.onGive = function() end
		end
		if item.actions["Drop"] and not item.actions["Give"] then
			item.actions["Give"] = {
				name = "Give",
				action = function() return true end,
				condition = item.actions["Drop"].condition or function() return true end,
			}
		end
		if item.actions["Drop"] or item.actions["Give"] and not item.actions["Transfer"] then
			item.actions["Transfer"] = {
				name = "Transfer",
				action = function() return true end,
				condition = item.actions["Give"].condition or item.actions["Drop"].condition,
			}
		end

	end

	cityrp.item.stored[item.uniqueID] = item

	if item.ItemRegistered then
		item:ItemRegistered()
	end
end

--- Get an item by its name.
-- @string name The name or unique ID of the item to get.
-- @rtab[opt] The item table, or nil if not found.
--- @param name string
--- @return base_item?
function cityrp.item.get(name)
	if cityrp.item.stored[name] then
		return cityrp.item.stored[name]
	end

	name = string.lower(name)
	if cityrp.item.stored[name] then
		return cityrp.item.stored[name]
	end

	local partial = false
	local itemName
	for _, item in pairs(cityrp.item.stored) do
		itemName = string.lower(item.name)

		-- Full name match
		if name == itemName then
			return item
		end

		-- If we get a partial match, then store it, in-case we get a direct name match later.
		if string.find(itemName, name, 1, true) then
			partial = item
		end
	end

	if partial then
		return partial
	end
end

--- Add a new category.
-- @string name The name of the category to add.
-- @string description The description of the category. Defaults to an empty string.
-- @string access The access for the category, defaults to 'b'.
-- @treturn number The category index.
function cityrp.item.addCat(name, description, access)
	local data = {
		name = name or "",
		description = description or "",
		acccess = access or "b",
		index = cityrp.item.categoryIndex,
	}

	cityrp.item.cats[data.name] = data
	cityrp.item.catIndex = cityrp.item.catIndex + 1

	return data.index
end

--- Retrieve a category.
-- @string name Name of the category to find.
-- @treturn table Category data.
function cityrp.item.findCat(name)
	local item

	-- Check if we have a number meaning its an index.
	for k, v in pairs(cityrp.item.cats) do
		if (string.find(string.lower(v.name), string.lower(name))) then
			if (item) then
				if (string.len(v.name) < string.len(item.name)) then
					item = v
				end
			else
				item = v
			end
		end
	end
	-- Return the item that we found.

	return item
end

function cityrp.item.GetItemName(uniqueID)
	local item = cityrp.item.stored[uniqueID]
	if item then
		return item.name or "Unknown"
	end
end

if (cityrp.item.catIndex == 1) then
	cityrp.item.addCat("Vehicles", "Used to get around the city.", "b")
	cityrp.item.addCat("Government Vehicles", "Government Vehicles", "b")
	cityrp.item.addCat("Public Transportation", "Public Transportation Vehicles", "b")
	cityrp.item.addCat("Weapons", "Weapons which can be useful in a fight.", "b")
	cityrp.item.addCat("Weapon Modifications", "Legal weapon attachments and modifications.", "b")
	cityrp.item.addCat("Trucks and trailers", "Trucks and Trailers", "b")
	cityrp.item.addCat("Delivery Trucks", "Delivery Trucks", "b")
	cityrp.item.addCat("Boats", "Tour around the lake on a boat!", "b")
	cityrp.item.addCat("Black Market Weapon Modifications", "Highly illegal weapon attachments and modifications.", "b")
	cityrp.item.addCat("Fishing", "Rods, bait and lures used for fishing", "b")
	cityrp.item.addCat("Food", "Used to restore hunger.", "b")
	cityrp.item.addCat("Agriculture", "Resources and materials for farming.", "b")
	cityrp.item.addCat("Shopkeeping", "Shopkeeping resources.", "b")
	cityrp.item.addCat("Moonshine", "Moonshining stuff.", "b")
	cityrp.item.addCat("Ingredients", "Used to create items.", "b")
	cityrp.item.addCat("Ammunition", "Is your gun dying for some fuel?", "b")
	cityrp.item.addCat("Weapon Modifications", "Upgrade your weapons here", "b")
	cityrp.item.addCat("Black Market", "All the luxuries of life.", "b")
	cityrp.item.addCat("Mixtures", "Find out how to make items.", "b")
	cityrp.item.addCat("Contraband", "Earn money over time.", "b")
	cityrp.item.addCat("Misc.", "All of the other stuff.", "b")
	cityrp.item.addCat("Clothing", "Clothes which you put on, some are donator or RPP point restricted.", "b")
	cityrp.item.addCat("Pharmaceuticals", "Used to aid players.", "b")
	cityrp.item.addCat("Packaging", "Used to hold things together.", "b")
	cityrp.item.addCat("Vehicle Maintenance", "Fix up your vehicle.", "b")
	cityrp.item.addCat("Pollux", "Test Vehicles.", "b")
	cityrp.item.addCat("Milestones", "Milestone rewards.", "b")
	cityrp.item.addCat("BETA Items", "Items for BETA Testers", "b")
	cityrp.item.addCat("Mining", "All items related to mining.", "b")
end

local itemFolders = {
	"ammunition",
	"cars",
	"clothing",
	"contraband",
	"food",
	"ingredients",
	"mining",
	"mixtures",
	"other",
	"milestones",
	"weapons",
	"pharmaceuticals",
	"blackmarket",
	"packaging",
	"shopkeeping",
}

function cityrp.item.Initialize()
	logger:Info("Loading Core Items")
	for _, folder in ipairs(itemFolders) do
		cityrp.util.IncludeDir(GMFOLDER .. "/gamemode/core/items/" .. folder, "shared")
		logger:Info("Loaded ", hl(folder))
	end
	hook.Run("InitializedItems")
end

function cityrp.item.InitializePostBaseItems()
	logger:Info("Loading Post Base Items")
	cityrp.util.IncludeDir(GMFOLDER .. "/gamemode/core/items/postbase", "shared")
	logger:Info("Loaded ", hl("Post Base"))
end

function cityrp.item.InitializeBaseItems()
	logger:Info("Loading Base Items")
	cityrp.util.IncludeDir(GMFOLDER .. "/gamemode/core/items/base", "shared")
	logger:Info("Loaded ", hl("base"))
	cityrp.item.InitializePostBaseItems()
	hook.Run("InitializedBaseItems")
end
