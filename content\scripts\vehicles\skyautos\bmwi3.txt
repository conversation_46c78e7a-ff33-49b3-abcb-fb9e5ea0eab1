	// bmwi3, created by [<PERSON>] Conn in Fri Jun 24 16:42:58 2022, using Vehicle Controller (VCMod).

	"Vehicle"
	{
		"WheelsPerAxle"		"2"
		"Body"
		{
			"CounterTorqueFactor"	"1.2"
			"MassCenterOverride"	"0 0 -2"
			"MassOverride"			"1600"
			"AddGravity"			"1.2"
			"MaxAngularVelocity"	"520"
		}
		"Engine"
		{
			"HorsePower"			"105"
			"MaxRPM"				"8000"
			"MaxSpeed"				"60"
			"MaxReverseSpeed"		"35"
			"AutobrakeSpeedGain"	"1.1"
			"AutobrakeSpeedFactor"	"3"
			"Autotransmission"		"0"
			"AxleRatio"				"10"
			"Gear"					"9"
			"Gear"					"0.00001"


			"ShiftUpRPM"			"7000"
			"ShiftDownRPM"			"4000"
		}
		"Steering"
		{
			"DegreesSlow"						"40"
			"DegreesFast"						"10"
			"DegreesBoost"						"11"
			"FastDampen"						"100"
			"SteeringExponent"					"0"
			"SlowCarSpeed"						"5"
			"FastCarSpeed"						"40"
			"SlowSteeringRate"					"3.3"
			"FastSteeringRate"					"2.1"
			"SteeringRestRateSlow"				"2.8"
			"SteeringRestRateFast"				"1.6"
			"TurnThrottleReduceSlow"			"0.01"
			"TurnThrottleReduceFast"			"2"
			"BrakeSteeringRateFactor"			"2.2"
			"ThrottleSteeringRestRateFactor"	"2"
			"BoostSteeringRestRateFactor"		"1.7"
			"BoostSteeringRateFactor"			"1.7"

			"PowerSlideAccel"					"200"

			"SkidAllowed"						"1"
			"DustCloud"							"1"
		}
		"Axle"
		{
			"Wheel"
			{
				"Radius"						"15"
				"Mass"							"350"
				"Inertia"						"0"
				"Damping"						"0.2"
				"RotDamping"					"0.5"
				"Material"						"jeeptire"
				"SkidMaterial"					"slidingrubbertire"
				"BrakeMaterial"					"brakingrubbertire"
			}
			"Suspension"
			{
				"SpringConstant"				"95"
				"SpringDamping"					"0.8"
				"StabilizerConstant"			"0"
				"SpringDampingCompression"		"9"
				"MaxBodyForce"					"30"
			}
			"TorqueFactor"						"0.1"
			"BrakeFactor"						"0.75"
		}
		"Axle"
		{
			"Wheel"
			{
				"Radius"						"15"
				"Mass"							"350"
				"Inertia"						"0"
				"Damping"						"0.3"
				"RotDamping"					"0.6"
				"Material"						"jeeptire"
				"SkidMaterial"					"slidingrubbertire"
				"BrakeMaterial"					"brakingrubbertire"
			}
			"Suspension"
			{
				"SpringConstant"				"100"
				"SpringDamping"					"0.8"
				"StabilizerConstant"			"0"
				"SpringDampingCompression"		"9"
				"MaxBodyForce"					"25"
			}
			"TorqueFactor"						"1"
			"BrakeFactor"						"0.7"
		}
	}

	"Vehicle_Sounds"
	{
		"Gear"
		{
			"Max_Speed"				"0.01"
			"Speed_Approach_Factor"	"1"
		}
		"Gear"
		{
			"Max_Speed"				"0.95"
			"Speed_Approach_Factor"	"0.01"
		}
		"Gear"
		{
			"Max_Speed"				"1.1"
			"Speed_Approach_Factor"	"0.05"
		}
		"State"
		{
			"Name"		"SS_GEAR_2_RESUME"
			"Sound"		"vehicles\skyautos\electricmotor_small\cruise.mp3"
			"Min_Time"	"0.5"
		}
		"State"
		{
			"Name"		"SS_REVERSE"
			"Sound"		"vehicles\skyautos\electricmotor_small\first.mp3"
			"Min_Time"	"0.5"
		}
		"State"
		{
			"Name"		"SS_GEAR_1_RESUME"
			"Sound"		"vehicles\skyautos\electricmotor_small\first.mp3"
			"Min_Time"	"0.5"
		}
		"State"
		{
			"Name"		"SS_GEAR_1"
			"Sound"		"vehicles\skyautos\electricmotor_small\first.mp3"
			"Min_Time"	"0.5"
		}
		"State"
		{
			"Name"		"SS_SLOWDOWN_HIGHSPEED"
			"Sound"		"vehicles\skyautos\electricmotor_small\slowdown.mp3"
			"Min_Time"	"0"
		}
		"State"
		{
			"Name"		"SS_START_IDLE"
			"Sound"		"vehicles\skyautos\electricmotor_small\i3startup.mp3"
			"Min_Time"	"9"
		}
		"State"
		{
			"Name"		"SS_GEAR_2"
			"Sound"		"vehicles\skyautos\electricmotor_small\cruise.mp3"
			"Min_Time"	"0.5"
		}
		"CrashSound"
		{
			"Min_Speed"			"350"
			"Min_Speed_Change"	"250"
			"Sound"				"atv_impact_medium"
			"Gear_Limit"		"1"
		}
		"CrashSound"
		{
			"Min_Speed"			"450"
			"Min_Speed_Change"	"350"
			"Sound"				"atv_impact_heavy"
			"Gear_Limit"		"0"
		}

		"Skid_LowFriction"		"common/null.mp3"
		"Skid_NormalFriction"	"common/null.mp3"
		"Skid_HighFriction"		"common/null.mp3"
	}
