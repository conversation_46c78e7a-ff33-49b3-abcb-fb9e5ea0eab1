--- @class base_swep : base_equip
--- @field SetSwepClass fun(self: self, swepClass: string): self Class name of the SWEP that will be equipped
--- @field GetSwepClass fun(self: self): string Gets class name of the SWEP this item equips
local ITEM = ITEM:New("base_equip")
	:SetName("Base Equippable Clothing")
	:SetCost(0)
	:SetStore(0)
	:SetSize(0)
	:SetCategory("Other") --[[@as base_swep]]
	:SetDropOnDeath(DEATH_DROP_CUSTOM_NEVER)
	:DisableAutoActions()

	--- Sets this item's SWEP (class name of SWEP) that is equpped
	-- @string swepClass The class name of the SWEP to equip
	-- @function ITEM:SetSwepClass

	--- Gets this item's SWEP
	-- @function ITEM:GetSwepClass
	-- @rstring The SWEP's class, nil if not set
	:AutoFunction("SwepClass", "swepClass")(nil)

--- Called when the player tries to equip this item, implement in child base/item
---@param client Player
---@param item Item
---@return boolean # If the item can be equipped
function ITEM:CanPlayerEquip(item, client)

	local class = self:GetSwepClass()

	if not class or not weapons.GetStored(class) then
		client:Notify("Invalid Class for item " .. item:GetName() .. " (" .. item:GetID() .. ")", 1)
		return false
	end

	if self:PlayerHasSwepEquipped(item, client) then
		client:Notify("You already have this weapon equipped!", 1)
		return false
	end

	return true
end

--- Called when the player tries to un-equip this item, implement in child base/item
---@param client Player
---@param item Item
---@return boolean # If the item can be un-equipped
function ITEM:CanPlayerUnEquip(item, client)

	local class = self:GetSwepClass()

	if not class or not weapons.GetStored(class) then
		client:Notify("Invalid Class for SWEP on " .. item:GetName() .. " (" .. item:GetID() .. ")", 1)
		return false
	end

	if not self:PlayerHasSwepEquipped(item, client) then
		client:Notify("You do not have this item equipped!", 1)
		return false
	end

	return true
end

--- Called when the player equips this item, implement in child base/item.
--- Return the entity created if this makes some sort of SWEP, it'll then automatically set the entity to the item and vice versa.
---@param client Player
---@param item Item
---@return Entity?
function ITEM:OnPlayerEquip(item, client)

	local wep = client:Give(self:GetSwepClass(), true)

	if not IsValid(wep) then
		client:Notify("Failed to give SWEP for " .. item:GetName() .. " (" .. item:GetID() .. ")", 1)
		return
	end

	client:SelectWeapon(self:GetSwepClass())

	return wep
end

--- Called when the player un-equips this item, implement in child base/item
--- If an entity was passed when equipped, this won't automatically remove it from the player, you'll need to do that yourself. This is to let you save any important data and such before removing it.
---@param client Player
---@param item Item
function ITEM:OnPlayerUnEquip(item, client)

	local wep = item:GetEntity() --[[@as Weapon]]

	if not wep or not IsValid(wep) then
		client:Notify("SWEP invalid for " .. item:GetName() .. " (" .. item:GetID() .. ")", 1)
		return
	end

	client:SetActiveWeapon(NULL)
	client:StripWeapon(self:GetSwepClass())
	client:SelectWeapon("cityrp_keys")

	return
end

--- Called when the player dies if DropOnDeath is enabled, implement in child base/item if custom death override is enabled
---@param item Item
---@param client Player
function ITEM:OnPlayerDeath(item, client)

	local deathBehaviour = self:GetDropOnDeath()

	if deathBehaviour == DEATH_DROP_CUSTOM_NEVER then return end

	if not item:GetData("equipped", false) and deathBehaviour == DEATH_DROP_CUSTOM_EQUIPPED then return end

	item:SetData("equipped", false)
	item:DropEnt(1, client:GetPos(), client)
end

function ITEM:PlayerHasSwepEquipped(item, client)
	if client:HasWeapon(self:GetSwepClass()) or IsValid(item:GetEntity()) then
		return true
	end
	return false
end

ITEM:AutoRegister()
