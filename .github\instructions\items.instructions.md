---
applyTo: "**/*item*/**"
---

# Item System Instructions

This document describes CityRP's modern item system, which uses a template-based architecture to separate reusable behavior (bases) from item configuration (definitions).

## System Overview

The modern item system consists of:
- **Item Bases (Templates)**: Reusable behavior patterns that define how item types work
- **Item Definitions**: Configuration-only files that create actual items using bases
- **Action Builder System**: Modern replacement for legacy onUse/canUse functions
- **Lifecycle Hooks**: Clean event system for item registration, creation, and player events
- **Auto-Action Conversion**: Backwards compatibility layer for legacy items

## Legacy Conversion Checklist
When updating legacy items:
1. **Identify item type and behavior** - legacy items contain all logic in one file
2. **Create or find appropriate base** - legacy items don't use bases, so create a reusable base for the item type if none exists
3. **Extract reusable behavior to base** - move common functionality (actions, lifecycle hooks) to the base
4. **Convert item to definition** - reduce legacy item file to just property configuration inheriting from base
5. **Replace legacy patterns**:
   - `onUse`/`canUse` → action builder with condition/action functions
   - `onDrop`/`canDrop` → action builder or lifecycle hooks
   - Direct function definitions → base methods
   - Legacy metatable hooks → modern lifecycle hooks and action system
6. **Test all functionality** after conversion

**Note**: Legacy items are standalone files with embedded behavior. The modern system separates behavior (bases) from configuration (item definitions). This template-item design is new and should be used for all new items.

**Warning**: Avoid using legacy metatable functions (onUse, canUse, onRemove, canRemove, onDrop, canDrop, onGive, canGive, etc.) - these exist only for backwards compatibility. Use modern lifecycle hooks and action system instead.

## Item System Evolution
CityRP has evolved through three different item systems:

1. **Legacy System (Deprecated)**: Single files with embedded behavior, no inheritance
2. **Intermediate System (Deprecated)**: Added some structure but still mixed behavior with configuration
3. **Modern System (Current)**: Clean separation with bases (templates) and definitions (configurations)

**AI Decision Rule**: Only use the modern template-item system. When encountering legacy items, convert them following the legacy conversion checklist.

## Modern Item System Requirements
- Use `ITEM:New("base_name")` for all items and bases
- Use method chaining with `:SetProperty()` calls
- Use `:AutoFunction()` for auto-generating getter/setter methods
- Use action builder pattern with `:AddAction("ActionName"):SetCondition():SetAction():Build()`
- Use `:AutoRegister()` to complete item registration
- Never use legacy direct function overrides (`onUse`, `canAction`, etc.)

**Important**: The base item metatable contains many legacy functions (onUse, canUse, onRemove, etc.) for backwards compatibility. Do not use these legacy patterns in new items - only use the modern lifecycle hooks and action system.

## Core Template vs Custom Bases

**Item Bases** are templates that define reusable behavior and eliminate code duplication:
- All item functionality is implemented in bases, not individual item files
- Items with similar behavior share a common base (e.g., all weapons use `base_swep`)
- Bases contain actions, lifecycle hooks, and custom methods
- Items inherit everything from their base and only set unique properties

**The Purpose**: Items tend to have the same or common functionality and simply differ in appearance, stats, or configuration. Bases prevent copying the same code across hundreds of similar items.

**Available Core Bases**:
- `base_item`: Root base for all items (provides basic properties and lifecycle)
- `base_equip`: Equipment items that can be equipped/unequipped with slot management
- `base_swep`: Weapon items that give SWEPs when equipped (inherits from base_equip)
- `base_place`: Items that can be placed as entities in the world

## File Placement Strategy

**Item Bases (Templates/Blueprints):**
- Core Templates: `gamemode/core/items/base/sh_base_mytype.lua`
- Advanced Templates: `gamemode/core/items/postbase/sh_base_advanced.lua` (inherit from other bases)
- Plugin Templates: `plugins/<plugin_name>/items/sh_myplugin_base.lua`

**Item Definitions (Actual Usable Items):**
- Plugin Items: `plugins/<plugin_name>/items/sh_myitem.lua`
- Core Items: `gamemode/core/items/**/sh_itemname.lua`

**Loading Order**: `base/` → `postbase/` → item definitions (ensures inheritance works correctly)

## Item Base Structure (Templates)
```lua
--- @class base_mytype : base_item
--- @field SetMyProperty fun(self: self, value: type): self Description of property
--- @field GetMyProperty fun(self: self): type Gets the property value
local ITEM = ITEM:New("base_parent")
    :SetName("Base Template Name")
    :SetCost(0)
    :SetStore(0)
    :SetSize(1)
    :SetCategory("Category") --[[@as base_mytype]]
    :DisableAutoActions()

    --- Auto-generate getter/setter for custom properties
    :AutoFunction("MyProperty", "myProperty")(defaultValue)

-- Override or extend base_item lifecycle hooks as needed
-- See core metatable for signature details

function ITEM:ItemRegistered()
    -- Called once when this base is registered
    -- Use for one-time setup like registering hooks
end

function ITEM:ItemCreated(item)
    -- Called when item instance is created
    -- Use for setting initial item data
    if SERVER then
        item:SetData("myProperty", self:GetMyProperty())
    end
end

function ITEM:PlayerSpawnHook(item, client)
    -- Called when player spawns with this item
    -- Use for equipment state management
end

function ITEM:PlayerDeathHook(item, client)
    -- Called when player dies with this item
    -- Use for custom death behavior (dropping, etc.)
end

-- Define custom behavior methods
function ITEM:MyCustomMethod(item, client)
    -- Base-specific functionality
    return true
end

-- Create action condition and action functions
local function UseItemCondition(baseItem, item, client)
    -- Validation logic for using this item type
    return client:IsValid() and item:IsValid()
end

local function UseItemAction(baseItem, item, client)
    -- Server-side action logic for using this item type
    item:Remove()
    return true
end

-- Build actions using action builder
ITEM:AddAction("Use")
    :SetName("Use Item")
    :SetIcon("icon16/accept.png")
    :SetCondition(UseItemCondition)
    :SetAction(UseItemAction)
    :SetSortRank(10)
    :Build()

ITEM:AutoRegister()
```

**Key Rules for Bases:**
- Use `:DisableAutoActions()` to prevent legacy auto-conversion
- Define lifecycle hooks directly on ITEM table
- Create local functions for action conditions and actions
- Use action builder pattern with `:SetCondition()`, `:SetAction()`, `:Build()`
- Include LuaLS class annotations for type safety
- Bases are templates - not used directly by players

## Item Definition Structure (Actual Items)
```lua
ITEM:New("base_mytype")
    :SetName("Specific Item Name")
    :SetDescription("Item description")
    :SetModel("models/path.mdl")
    :SetCost(100)
    :SetSize(3)
    :SetCategory("Category") --[[@as base_mytype]]
    :SetMyProperty("custom_value") --[[@as base_mytype]]
    :AutoRegister()
```

**Key Rules for Definitions:**
- Inherit from existing base only
- Keep minimal - just property configuration
- Never call `:DisableAutoActions()` or `:AddAction()`
- Never define custom methods or lifecycle hooks
- Use type annotations `--[[@as base_name]]` after base-specific properties
- Always end with `:AutoRegister()`
- These create actual items that appear in inventories

## Lifecycle Hooks Reference
The following lifecycle hooks are available for implementation in bases:

**Registration Hooks:**
- `ItemRegistered()`: Called once when base/item is registered
- `ItemCreated(item)`: Called when item instance is created

**Player Event Hooks:**
- `PlayerSpawnHook(item, client)`: Called when player spawns with item
- `PlayerDeathHook(item, client)`: Called when player dies with item

**Legacy Compatibility Hooks (Deprecated):**
- `onUse`, `canUse`, `onRemove`, `canRemove`, `onDrop`, `canDrop`, `onGive`, `canGive`
- These are automatically converted to actions if `DisableAutoActions()` is not called
- Do not use these in new bases - use action builder system instead

## Action Builder System
The modern action system uses a builder pattern to replace legacy onUse/canUse functions:

```lua
-- Define action functions first
local function MyActionCondition(baseItem, item, client, ...)
    -- Validation logic - return false to prevent action
    if not client:IsValid() then return false, "Invalid player" end
    if not item:IsValid() then return false, "Invalid item" end
    return true -- Allow action
end

local function MyActionAction(baseItem, item, client, ...)
    -- Server-side action logic
    client:Notify("Used " .. item:GetName())
    return true -- Return false to prevent item removal (for Use action)
end

-- Build the action
ITEM:AddAction("MyAction")
    :SetName("Display Name")
    :SetIcon("icon16/accept.png")
    :SetCondition(MyActionCondition)
    :SetAction(MyActionAction)
    :SetSortRank(10) -- Lower numbers appear first
    :Build()
```

**Common Action IDs:**
- `"Use"`: Primary item usage (replaces onUse/canUse)
- `"Drop"`: Drop item from inventory (replaces onDrop/canDrop)
- `"Give"`: Give item to another player (replaces onGive/canGive)
- `"Remove"`: Remove item from inventory (replaces onRemove/canRemove)
- `"Equip"`: Equip item (for base_equip items)
- `"Unequip"`: Unequip item (for base_equip items)
- `"Place"`: Place item as entity (for base_place items)

**Action Function Signatures:**
- Condition: `function(baseItem, item, client, ...additionalArgs) -> boolean, string?`
- Action: `function(baseItem, item, client, ...additionalArgs) -> boolean?`

## Equipment System (base_equip)
For items that can be equipped/unequipped:

```lua
--- @class base_myequip : base_equip
local ITEM = ITEM:New("base_equip")
    :SetName("Base My Equipment")
    :SetEquipSlot("myslot") -- Unique slot identifier
    :SetDropOnDeath(DEATH_DROP_EQUIPPED) -- Drop behavior on death
    :DisableAutoActions()

-- Override equipment behavior
function ITEM:CanPlayerEquip(item, client)
    -- Custom equip validation
    return client:IsValid()
end

function ITEM:OnPlayerEquip(item, client)
    -- Called when equipped - return entity if creating one
    client:Notify("Equipped " .. item:GetName())
    return nil -- or return created entity
end

function ITEM:OnPlayerUnEquip(item, client)
    -- Called when unequipped
    client:Notify("Unequipped " .. item:GetName())
end

ITEM:AutoRegister()
```

**Death Drop Constants:**
- `DEATH_DROP_NEVER`: Never drop on death (default)
- `DEATH_DROP_EQUIPPED`: Drop only if equipped
- `DEATH_DROP_ALWAYS`: Always drop on death
- `DEATH_DROP_CUSTOM`: Use custom OnPlayerDeath hook

## AutoFunction System
Generate getter/setter methods automatically:

```lua
-- Creates SetMyProperty() and GetMyProperty() methods
ITEM:AutoFunction("MyProperty", "myProperty")(defaultValue)

-- Usage in item definitions:
ITEM:New("base_mytype")
    :SetMyProperty("custom_value") --[[@as base_mytype]]
```

## Auto-Action Conversion (Legacy Compatibility)
Legacy items with onUse/canUse functions are automatically converted to actions unless `:DisableAutoActions()` is called:

```lua
-- Legacy pattern (automatically converted):
function ITEM:onUse(item, client) return true end
function ITEM:canUse(item, client) return true end

-- Becomes equivalent to:
ITEM:AddAction("Use")
    :SetCondition(function(base, item, client) return base:canUse(item, client) end)
    :SetAction(function(base, item, client) return base:onUse(item, client) end)
    :Build()
```

**Converted Legacy Functions:**
- `onUse`/`canUse` → "Use" action
- `onDrop`/`canDrop` → "Drop" action
- `onGive`/`canGive` → "Give" action
- `onRemove`/`canRemove` → "Remove" action

## Legacy Conversion Process
When converting legacy items:
1. **Identify the item type** - what behavior does it have?
2. **Find or create a base** - does a base for this type exist?
3. **Extract behavior to base** - move all functions, actions, hooks to the base
4. **Reduce item to configuration** - leave only property settings
5. **Convert legacy patterns**:
   - `onUse`/`canUse` → action builder with condition/action functions
   - Direct function definitions → base methods

## Do / Don't
**Do:**
- Create reusable bases for item types
- Use action builder pattern for all interactions
- Keep item definitions minimal (configuration only)
- Define behavior in bases, not individual items
- Use modern lifecycle hooks and action system
- Use `:DisableAutoActions()` in bases to prevent legacy conversion

**Don't:**
- Define actions or methods in item definitions
- Use legacy metatable hooks (onUse, canUse, onRemove, etc.) in new code
- Copy behavior between similar items
- Mix configuration with behavior
- Use legacy direct function overrides
- Rely on auto-action conversion for new items

## Complete Examples

### Example 1: Simple Consumable Base
```lua
--- @class base_consumable : base_item
--- @field SetRestoreAmount fun(self: self, amount: number): self
--- @field GetRestoreAmount fun(self: self): number
local ITEM = ITEM:New()
    :SetName("Base Consumable")
    :SetCategory("Consumables")
    :DisableAutoActions()
    :AutoFunction("RestoreAmount", "restoreAmount")(10)

local function ConsumeCondition(baseItem, item, client)
    if not client:IsValid() then return false, "Invalid player" end
    if client:Health() >= client:GetMaxHealth() then
        return false, "You are already at full health"
    end
    return true
end

local function ConsumeAction(baseItem, item, client)
    local amount = baseItem:GetRestoreAmount()
    client:SetHealth(math.min(client:Health() + amount, client:GetMaxHealth()))
    client:Notify("Restored " .. amount .. " health")
    return true -- Remove item after use
end

ITEM:AddAction("Use")
    :SetName("Consume")
    :SetIcon("icon16/heart.png")
    :SetCondition(ConsumeCondition)
    :SetAction(ConsumeAction)
    :Build()

ITEM:AutoRegister()
```

### Example 2: Weapon Item Definition
```lua
ITEM:New("base_swep")
    :SetName("Crowbar")
    :SetDescription("A sturdy crowbar for breaking things")
    :SetModel("models/weapons/w_crowbar.mdl")
    :SetCost(150)
    :SetSize(3)
    :SetCategory("Weapons") --[[@as base_swep]]
    :SetSwepClass("cityrp_crowbar") --[[@as base_swep]]
    :SetEquipSlot("primary") --[[@as base_swep]]
    :SetDropOnDeath(DEATH_DROP_EQUIPPED) --[[@as base_swep]]
    :AutoRegister()
```

### Example 3: Legacy Item Conversion
```lua
-- BEFORE (Legacy):
local ITEM = {}
ITEM.name = "Health Potion"
ITEM.cost = 50
ITEM.model = "models/props_junk/PopCan01a.mdl"

function ITEM:onUse(item, client)
    client:SetHealth(client:GetMaxHealth())
    client:Notify("Health restored!")
    return true
end

function ITEM:canUse(item, client)
    return client:Health() < client:GetMaxHealth()
end

cityrp.item.register(ITEM)

-- AFTER (Modern):
ITEM:New("base_consumable")
    :SetName("Health Potion")
    :SetCost(50)
    :SetModel("models/props_junk/PopCan01a.mdl") --[[@as base_consumable]]
    :SetRestoreAmount(100) --[[@as base_consumable]]
    :AutoRegister()
```

## Migration Checklist
When updating the item system:

1. **Audit existing items** - identify legacy patterns
2. **Create missing bases** - group similar items by behavior
3. **Convert legacy items** - extract behavior to bases, reduce to configuration
4. **Test thoroughly** - ensure all functionality works
5. **Update documentation** - reflect new structure
6. **Train developers** - ensure team understands new patterns

## Troubleshooting

**Common Issues:**
- **Actions not appearing**: Check if `:DisableAutoActions()` is preventing auto-conversion
- **Type errors**: Ensure proper `--[[@as base_name]]` annotations
- **Missing methods**: Verify base inheritance chain and AutoFunction calls
- **Legacy functions still working**: Auto-conversion is active, consider migrating to action system

**Debug Tips:**
- Use `print(item:GetBase())` to verify inheritance
- Check `item.actions` table to see available actions
- Verify loading order: bases before definitions
- Test with `:DisableAutoActions()` to isolate modern vs legacy behavior
