Generate a commit message for my project following the Conventional Commits format with gitmoji. The structure must be exactly:

```
<type>[optional scope]: <gitmoji> <description>

[optional body]
```

Examples:
- `feat(net): :sparkles: add new optimised net library`
- `feat: :sparkles: add new vehicle item`
- `fix(inventory): :bug: fix invalid inventory on client rejoin`
- `fix: :bug: client error when joining server`
- `docs: :memo: update README installation steps`
- `docs: :bookmark: add LuaLS comments to inventory functions`
- `style: :art: format code according to linting rules`
- `refactor: :recycle: changed plugin loading logic`

Types must be one of:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes including README updates, Lua<PERSON> comments, and code documentation
- `style`: Changes not affecting code functionality (formatting, whitespace, etc)
- `refactor`: Code structure changes that neither fix bugs nor add features
- `perf`: Code change that improves performance
- `test`: Adding or correcting tests
- `build`: Changes affecting build system or dependencies
- `ci`: Changes to CI configuration such as GitHub Actions
- `chore`: Other changes not modifying src or test files
- `revert`: Reverting a previous commit

Optional scopes must be the name of either the library or plugin being edited, which may not always be the name of the file. For example, a plugin called exampleplugin which had a file called sh_plugin.lua added would use feat(exampleplugin). A library called options would be feat(options). You should not include a scope where one is not clear or is too generic.

Important: Use `docs` (with :bulb: or :memo:) for LuaLS additions/changes, do not use `refactor` for annotation or comment changes!
Important: Make sure you specify LuaLS annotations in the commit message for LuaLS additions/changes!

Always include a relevant gitmoji code at the start of the description. Match the gitmoji to the type of change.

The following gitmojis may be used:
- 🎨 `:art:` — Code style (improve structure/format)
- ⚡️ `:zap:` — Performance improvement
- 🔥 `:fire:` — Remove code or files
- 🐛 `:bug:` — Bug fix
- 🚑 `:ambulance:` — Critical hotfix
- ✨ `:sparkles:` — New feature
- 📝 `:memo:` — Documentation update
- ♻️ `:recycle:` — Code refactor
- ✅ `:white_check_mark:` — Add or update tests
- 🚀 `:rocket:` — Deploy or release
- 🔒 `:lock:` — Security fix
- 🔧 `:wrench:` — Configuration change
- 🔨 `:hammer:` — Build or dev-script update
- 🔀 `:twisted_rightwards_arrows:` — Merge branches
- ⏪️ `:rewind:` — Revert changes
- 💡 `:bulb:` — Add or update comments in source code.

For the body:
- Use bullet points (`*`) for multiple items
- Explain WHY the change was needed
- Include relevant context or technical details
- Keep the body as simple as possible, stick to only factual information in an unbiased format

Ensure the message clearly communicates the purpose of the commit. You may include humour or similar depending on the contents of the commit.
