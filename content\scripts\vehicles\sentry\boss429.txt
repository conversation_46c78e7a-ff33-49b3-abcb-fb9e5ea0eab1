"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"1.5"
		"MassCenterOverride"	"0 0 10"
		"MassOverride"			"3600"
		"AddGravity"			"0.99"
		"MaxAngularVelocity"	"120"
	}
	"Engine"
	{
		"HorsePower""444"
		"MaxRPM""6500"
		"MaxSpeed""150"
		"MaxReverseSpeed""35"
		"AutobrakeSpeedGain""1.1"
		"AutobrakeSpeedFactor""3"
		"Autotransmission""0"
		"AxleRatio""3.3"
		"Gear""4.0"
		"Gear""2.6"
		"Gear""1.0"
		"Gear""1.5"
		"Gear""1.2"

		"ShiftUpRPM""6000"
		"ShiftDownRPM""5500"



	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"30"
		"DegreesBoost"						"5"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"25"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"4"
		"SteeringRestRateFast"				"3"
		"TurnThrottleReduceSlow"			"0"
		"TurnThrottleReduceFast"			"0"
		"BrakeSteeringRateFactor"			"3"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"200"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"15.5"
			"Mass"							"450"
			"Damping"					"0"
			"RotDamping"					"0"
			"Material"					"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"60"
			"SpringDamping"					"0.7"
			"StabilizerConstant"			"2.5"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"1050"
		}
		"TorqueFactor"						"1.70"
		"BrakeFactor"						"0.50"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16.5"
			"Mass"							"450"
			"Damping"					"0.2"
			"RotDamping"					"0"
			"Material"					"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"60"
			"SpringDamping"					"1.7"
			"StabilizerConstant"			"2.5"
			"SpringDampingCompression"		"8.5"
			"MaxBodyForce"					"1050"
		}
		"TorqueFactor"						"1.80"
		"BrakeFactor"						"0.5"
	}
}


"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.1"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.3"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.5"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.7"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.9"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/SGMCars/boss429/start.mp3"
		"Min_Time"	"1.00"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/SGMCars/boss429/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/SGMCars/boss429/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/SGMCars/boss429/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/SGMCars/boss429/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/SGMCars/boss429/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/SGMCars/boss429/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/SGMCars/boss429/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/SGMCars/boss429/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/SGMCars/boss429/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/SGMCars/boss429/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/SGMCars/boss429/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/SGMCars/boss429/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/SGMCars/boss429/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/SGMCars/boss429/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/SGMCars/boss429/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/SGMCars/boss429/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
