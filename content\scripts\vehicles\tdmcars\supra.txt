// supra, created by TDM in 06/12/14 21:53:28, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 -9 2"
		"MassOverride"			"1510"
		"AddGravity"			"0.55"
		"MaxAngularVelocity"	"720"
	}
	"engine"
	{
		"horsepower"		"650"
		"maxrpm"		"6200"
		"maxspeed"		"110"		// mph
		"maxReverseSpeed"	"20"		// mph
		"autotransmission"	"1"
		"axleratio"		"4.56"
		"gear"			"1.86"		// 1st gear
		"gear"			"1.59"		// 2nd gear
		"gear"			"1.17"		// 3rd gear
		"gear"			"1.0"		// 4th gear
		"gear"			"0.84"		// 5th gear
		"shiftuprpm"		"3500"
		"shiftdownrpm"		"1000"

			"boost"
		{
			"force"		"1.5"	// 1.5 car body mass * gravity * inches / second ^ 2
			"duration"	"3.0"	// 3.0 second of boost
			"delay"		"3.0"	// 3 seconds before you can use it again
			"torqueboost"	"1"	// enable "sprint" mode of vehicle, not	force type booster
			"maxspeed"	"150"	// maximum turbo speed
		}

	}
	"steering"
	{
		"degreesSlow"		"45"	// steering cone at zero to slow speed
		"degreesFast"		"14"	// steering cone at fast speed to max speed
		"degreesBoost"		"5"	// steering cone at max boost speed (blend toward this after max speed)
		"steeringExponent"	"1.4"	// steering function is linear, then raised to this power to be slower at the beginning of the curve, faster at the end
		"slowcarspeed"		"15"
		"fastcarspeed"		"30"
		"slowSteeringRate"	"4.0"
		"fastSteeringRate"	"2.0"
		"steeringRestRateSlow"	"4.0"
		"steeringRestRateFast"	"2.0"
		"turnThrottleReduceSlow" "0.1"
		"turnThrottleReduceFast" "0.9"
		"brakeSteeringRateFactor"	"2"
		"throttleSteeringRestRateFactor"	"2"
		"boostSteeringRestRateFactor"	"1.7"
		"boostSteeringRateFactor"	"1.7"

		"powerSlideAccel"	"250"

		"skidallowed"		"1"
		"dustcloud"		"1"
	}

	// front axle
	"axle"
	{
		"wheel"
		{
			"radius"	"16"
			"mass"		"200"
			"inertia"	"0.5"		// steady the car (fixes the oscillation of the axles about z)
			"damping"	"0.3"
			"rotdamping"	"0.0"
			"material"	"jeeptire"
			"skidmaterial"	"slidingrubbertire"
			"brakematerial" "brakingrubbertire"
		}
		"suspension"
		{
			"springConstant"		"140"
			"springDamping"			"0"
			"stabilizerConstant"		"110"
			"springDampingCompression"	"4"
			"maxBodyForce"			"250"
		}

		"torquefactor"	"0.8"
		"brakefactor"	"0.75"
	}

	// rear axle
	"axle"
	{
		"wheel"
		{
			"radius"	"16"
			"mass"		"200"
			"inertia"	"0.5"		// steady the car (fixes the oscillation of the axles about z)
			"damping"	"0.3"
			"rotdamping"	"0.0"
			"material"	"jeeptire"
			"skidmaterial"	"slidingrubbertire"
			"brakematerial" "brakingrubbertire"
		}
		"suspension"
		{
			"springConstant"		"200"
			"springDamping"			"0.7"
			"stabilizerConstant"		"110"
			"springDampingCompression"	"4"
			"maxBodyForce"			"250"
		}
		"torquefactor"	"0.5"
		"brakefactor"	"0.55"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.06"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.35"
		"Speed_Approach_Factor"	"0.08"
	}
	"Gear"
	{
		"Max_Speed"				"0.53"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.75"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.01"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"tdmcars\supra\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"tdmcars\supra\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"tdmcars\supra\rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"tdmcars\supra\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"tdmcars\supra\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"tdmcars\supra\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"tdmcars\supra\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"tdmcars\supra\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"tdmcars\slsamg\engineoff.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"tdmcars\supra\enginestart.mp3"
		"Min_Time"	"3"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"tdmcars\supra\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"tdmcars\supra\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"tdmcars\supra\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"tdmcars\supra\idle.mp3"
		"Min_Time"	"0.08"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"tdmcars\supra\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"tdmcars\supra\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		""
	"Skid_NormalFriction"	""
	"Skid_HighFriction"		""
}