--[[--
Contains a bunch of stuff to do with items.
TODO: Better description
]]


-- @module cityrp.item
cityrp.item = cityrp.item or {}
cityrp.item.stored = cityrp.item.stored or {}
cityrp.item.instances = cityrp.item.instances or {}
cityrp.item.queue = cityrp.item.queue or {}

cityrp.item.logger = cityrp.logging:GetLogger("Item"):SetLevel("ANY")
local logger = cityrp.item.logger

cityrp.util.Include("cityrp/gamemode/core/metatables/sh_item.lua")

--- Create a new item
--- This will create a new instance of an item and insert it into the DB. It will also add the item into the inventory object.
---
--- If the item if stackable, this will not fill in the stacks and instead just create new item stacks via recursion. This is to prevent overriding player preferences.
--- @warn Only basic validation here, see inventory:Create for a better method with validation if you just want to add a new item into an inventory.
--- @see inventory:Create
--- @realm server
--- @param inventory Inventory # The inventory object to add the item to
--- @param uniqueID string # The uniqueID of the item to create
--- @param creatorID string|Player # The steamid64 of the player who is creating the item. You can also pass the player object itself. Set to "0" if no one created this
--- @param stack? integer # The stack of the item that will be created, only used if the item is stackable. Defaults to 1. If the item isn't stackable just pass 1 or more.
--- @param noSync? boolean # If true, the item will not be instantly networked to the client. Use this if you're adding many items at once and manually sync once they're all done.
--- @param callback? function # The callback function to call when the item has been created. This won't call if it failed. The callback will be passed the item object, the inventory object, and any extra arguments you passed to this function. Will callback false if failed.
--- @param ... any # Extra arguments to pass to the callback function.
--- @return boolean # Returns true if the creation process was initiated, false otherwise.
function cityrp.item.Create(inventory, uniqueID, creatorID, stack, noSync, callback, ...)
	if not cityrp.item.get(uniqueID) then
		logger:Fatal("Attempted to create an item with an invalid uniqueID: " .. uniqueID)
		error("")
		return false
	end

	if isnumber(inventory) then
		inventory = cityrp.inventory.Get(inventory)
	end

	if not IsValid(inventory) then
		logger:Fatal("Attempted to create an item with an invalid inventory: " .. uniqueID)
		error("")
		return false
	end

	local args = { ... }

	stack = stack or 1

	if stack <= 0 then
		logger:Fatal("Attempted to create an item with a stack of 0 or less: " .. uniqueID)
		error("")
		return false
	end

	if type(creatorID) == "Player" then
		creatorID = creatorID:SteamID64()
	elseif not creatorID then
		creatorID = "0"
	end

	local baseItem = cityrp.item.get(uniqueID)
	local stackable = baseItem.stackable and baseItem.stackable or false
	local maxStack = stackable and baseItem.maxstack or nil ---@type number|nil

	if not stackable then
		if stack and stack > 1 then
			logger:Warning("Attemped to create non-stackble item", logger:Highlight(uniqueID), "with stack",
				logger:Highlight(stack), "stacks.", logger:Highlight("Stack was set to 1."))
		end
		stack = 1
	elseif stackable and stack > maxStack then -- We will create multiple items if the stack is too big
		local amount = stack
		local stacks = math.ceil(amount / maxStack)
		local remainder = amount % maxStack

		for i = 1, stacks do
			local stackAmount = maxStack
			if i == stacks and remainder > 0 then
				stackAmount = remainder
			end

			if i != stacks then -- This is to make sure the callback is only called once
				cityrp.item.Create(inventory, uniqueID, creatorID, stackAmount, true)
			else
				cityrp.item.Create(inventory, uniqueID, creatorID, stackAmount, noSync, callback, unpack(args))
			end
		end

		return true
	end

	-- TODO: Mass creating items might mess up if item doesn't exist at first?
	local query = mysql:Insert("items")
	query:Insert("inventory_id", inventory.id)
	query:Insert("unique_id", uniqueID)
	query:Insert("creator_id", creatorID)
	if stackable then
		query:Insert("stack", stack)
	end

	query:Insert("data", util.TableToJSON({}))

	query:Callback(function(result, status, lastID)
		if not result then
			logger:Fatal("Failed to create item: " .. uniqueID .. " in inventory: " .. inventory.id)
			callback(false, false)
			error("")
		end

		local item = cityrp.item.CreateInternal(uniqueID, lastID, inventory.id)
		item:SetStack(stack)
		inventory:AddItem(item, noSync, true)

		if callback then
			callback(item, inventory, unpack(args))
		end

		hook.Run("ItemCreated", item, inventory)
	end)
	query:Execute()

	return true
end

--- Get an item by it's ID
--- @realm shared
--- @param id integer # The ID of the item to get
--- @return Item? # The item object if it exists, nil if it doesn't
function cityrp.item.Get(id)
	return cityrp.item.instances[id]
end

--- Get the base item table by it's uniqueID
--- @realm shared
--- @param uniqueID string # The uniqueID of the item to get
--- @return table? # The base item table if it exists, nil if it doesn't
function cityrp.item.GetBase(uniqueID)
	return cityrp.item.stored[uniqueID]
end

--- Creates an internal representation of an item.
--- @param uniqueID string # The unique ID of the item base.
--- @param itemID integer # The unique database ID for this item instance.
--- @param invID integer # The ID of the inventory this item belongs to.
--- @return Item # The created item object.
function cityrp.item.CreateInternal(uniqueID, itemID, invID)
	local base = cityrp.item.GetBase(uniqueID)

	if not base then
		logger:Fatal("Item ", logger:Highlight(uniqueID), " created without valid base!")
		error("")
	end

	local item = cityrp.meta.item:New(itemID, uniqueID, invID or 0)
	cityrp.item.instances[itemID] = item

	hook.Run("ItemCreatedInternal", item, invID)
	if base.ItemCreated then
		base:ItemCreated(item)
	end

	return item
end

--- Creates a generic dropped item entity. Picking this up creates a new item instance.
--- @param uniqueID string # The unique ID of the item base to create.
--- @param amount integer # The stack amount for the item.
--- @param client Player # The player dropping the item.
--- @param pos? Vector # The position to spawn the item at. Defaults to player's trace hit position + offset.
--- @return Entity # The created cityrp_item entity.
function cityrp.item.CreateDroppedEntGen(uniqueID, amount, client, pos)
	if not pos then
		pos = client:GetEyeTrace().HitPos
		pos.z = pos.z + 16
	end

	local ent = ents.Create("cityrp_item") --[[@as cityrp_item]]
	ent:SetPos(pos)
	ent:SetItem(uniqueID, amount, client)
	ent:Spawn()
	ent:DropToFloor()
	ent:Activate()

	return ent
end

--- Creates a unique dropped item entity representing an existing item instance.
--- @param item Item # The item instance being dropped.
--- @param client Player # The player dropping the item.
--- @param pos? Vector # The position to spawn the item at. Defaults to player's trace hit position + offset.
--- @return Entity # The created cityrp_item entity.
function cityrp.item.CreateDroppedEnt(item, client, pos)
	if not pos then
		pos = client:GetEyeTrace().HitPos
		pos.z = pos.z + 16
	end

	local ent = ents.Create("cityrp_item")
	ent:SetPos(pos)
	ent:SetUniqueItem(item, client)
	ent:Spawn()
	ent:DropToFloor()
	ent:Activate()

	return ent
end

--- Loads items from the database for a given inventory.
--- @param inv Inventory # The inventory object to load items into.
--- @param callback? function # Function to call after items are loaded. Passes the inventory object and extra args.
--- @param ... any # Extra arguments to pass to the callback.
function cityrp.item.LoadFromInv(inv, callback, ...)
	local args = { ... }
	local id = inv:GetID()
	local query = mysql:Select("items")
	query:Select("item_id")
	query:Select("unique_id")
	query:Select("stack")
	query:Select("data")
	query:Where("inventory_id", id)
	query:WhereNull("deleted")

	query:Callback(function(result)
		if istable(result) and #result > 0 then
			for _, v in ipairs(result) do
				local item = cityrp.item.CreateInternal(v.unique_id, tonumber(v.item_id), id)

				local data = util.JSONToTable(v.data, nil, true) or {}
				item.data = data
				item.stack = v.stack
				inv:AddItem(item, false, true)
				hook.Run("ItemLoaded", item, inv)
			end

			if callback then
				callback(inv, unpack(args))
			end

			hook.Run("InventoryLoaded", inv)
		end
	end)

	query:Execute()
end

if SERVER then
	util.AddNetworkString("item_data")
	util.AddNetworkString("item_stack")
	util.AddNetworkString("item_create_admin")
	util.AddNetworkString("item_placement")
	util.AddNetworkString("item_syncOverrideAll")

	--- Make a client perform an inventory action, this should be used whenever you want the player to interact with an item on the server.
	--- On the client, you can use item:RequestAction() which will network to here.
	--- @realm server
	--- @param client Player # The client to perform the action
	--- @param item integer|Item # The ID of an item or the item itself
	--- @param action string # The action to perform
	--- @param ... any # The args to pass to the action
	function cityrp.item.Action(client, item, action, ...)
		print("cityrp.item.Action", client, item, action)
		logger:Debug("Player", tostring(client), " requestSetting action ", tostring(action), " on item ", tostring(item))

		if not IsValid(client) then return end

		if not item then return end

		if not isstring(action) then return end

		if not client:ValidState() then return end

		local args

		if istable(...) then
			args = ...
		else
			args = { ... }
		end

		local vClients = {}
		local vInts = {}
		local vVectors = {}

		for _, var in pairs(args) do
			if type(var) == "Player" then
				vClients[#vClients + 1] = var
			elseif type(var) == "number" then
				vInts[#vInts + 1] = var
			elseif type(var) == "Vector" then
				vVectors[#vVectors + 1] = var
			end
		end

		if istable(item) then
			item = item -- Already an item object
		else
			item = cityrp.item.Get(tonumber(item))
		end

		if not istable(item) and not isnumber(item) then -- Check if it's a valid item object or number ID
			client:Notify("Invalid item!")
			return
		end

		if not action then
			client:Notify("Invalid action!")
			return
		end

		local inv = item:GetInventory()
		if not inv then return end

		if (not inv:OnCheckAccess(client) and inv:GetOwner() != client) or not inv:CanAction(action, client, item) then
			client:Notify("You do not have access to this inventory!")
			return
		end

		if action == "Use" then
			item:PlayerUse(client)
		elseif action == "Remove" then
			item:PlayerRemove(client)
		elseif action == "Drop" then
			item:PlayerDrop(client, vInts[1])
		elseif action == "Give" then
			item:PlayerGive(client, vClients[1], vInts[1])
		elseif action == "Split" then
			item:PlayerSplit(client, vInts[1])
		elseif action == "Merge" then
			item:PlayerMerge(client, vInts[1])
		elseif action == "Place" then
			item:PlayerPlace(client, unpack(args))
		elseif action == "Transfer" then
			item:PlayerTransfer(client, unpack(args))
		else
			item:PlayerAction(client, action, unpack(args))
		end
	end

	--- Network receiver for item actions requested by clients.
	net.Receive("cityrp_item_action", function(len, client)
		client.lastItemAction = client.lastItemAction or 0

		if CurTime() - client.lastItemAction < 0.1 then
			client:Notify("Please wait before performing another action!", NOTIFY_ERROR)
			return
		end

		local id = net.ReadUInt(32)
		local action = net.ReadString()
		local data = net.ReadTable()


		if id and action then
			cityrp.item.Action(client, id, action, data)
		end

		client.lastItemAction = CurTime()
	end)

	--- Network receiver for admin item creation requests.
	net.Receive("item_create_admin", function(len, client)
		client.lastItemCreate = client.lastItemCreate or 0

		if CurTime() - client.lastItemCreate < 1 then
			client:Notify("Please wait before creating another item!", NOTIFY_ERROR)
			return
		end

		local uniqueID = net.ReadString()

		if not client:IsSuperAdmin() then
			client:Notify("You do not have permission to do this!", NOTIFY_ERROR)
			return
		end

		if not cityrp.item.get(uniqueID) then
			client:Notify("Invalid item!", NOTIFY_ERROR)
			return
		end

		client:Notify("Creating item, please wait for confirmation", NOTIFY_HINT)

		cityrp.acommands.adminDoTwo(client,
			client:Nick() .. " spawned in the following item: " .. cityrp.item.GetItemName(uniqueID), "icon16/basket_put")

		cityrp.item.Create(client:GetInventory(), uniqueID, client, 1, false, function(item, inv)
			client:Notify("Item " .. item:GetName() .. " has been created!", NOTIFY_HINT)
		end)

		client.lastItemCreate = CurTime()
	end)
end

if CLIENT then

	--- Network receiver to initiate item placement ghosting.
	net.Receive("item_placement", function()
		local id = net.ReadUInt(32)

		local item = cityrp.item.Get(id)
		local model = item.model

		if not item then return end

		local callback = function(pos, angles)
			print("callback", pos, angles)
			print("item", item, item.RequestAction)
			item:RequestAction("Place", { pos, angles })
		end
		cityrp.entity.StartGhost(model, callback)
	end)

	--- Network receiver for updating specific item data.
	net.Receive("item_data", function()
		local id = net.ReadUInt(32)
		local key = net.ReadString()
		local value = net.ReadType()

		---@type Item
		local item = cityrp.item.instances[id]
		if not item then return end

		item.data = item.data or {}

		item.data[key] = value

		local invID = item.inventoryID -- Corrected variable name

		hook.Run("InventoryUpdate", invID)
	end)

	--- Network receiver for updating item stack count.
	net.Receive("item_stack", function()
		local id = net.ReadUInt(32)
		local stack = net.ReadUInt(32)

		local item = cityrp.item.instances[id]
		if not item then return end

		item.stack = stack
	end)

	--- Network receiver for full item state synchronization (override).
	net.Receive("item_syncOverrideAll", function()
		local inv = net.ReadUInt(32)
		local sItem = net.ReadTable()

		local item = cityrp.item.Get(sItem.id)

		if not item then
			item = cityrp.item.CreateInternal(sItem.uniqueID, sItem.id, inv or 0)

			item.data = sItem.data or {}
			item.stack = sItem.stack

		elseif item:GetInventoryID() != inv then
			-- Item moved inventory, update its state
			item.inventoryID = inv -- Update inventory ID
			item.data = sItem.data or {}
			item.stack = sItem.stack
		else
			-- Item still in the same inventory, just update state
			item.data = sItem.data or {}
			item.stack = sItem.stack
		end
		-- Potentially trigger UI update here if needed
		hook.Run("InventoryUpdate", inv)
	end)
end

--- Hook callback for when the database connection is established. Runs crash protection.
hook.Add("DatabaseConnected", "runCrashProtection", function()
	logger:Info("Database Connected, running ", logger:Highlight("Crash Protection"), " in 10 seconds.")
	timer.Simple(10, function() -- On a delay just so it is more visible in console, no actual technical reason for it
		logger:Info("Running Item ", logger:Highlight("Crash Protection"), " system.")
		cityrp.item.RunCrashProtection()
	end)
end)

--- Runs a check to recover items that might have been lost due to a server crash while being transferred.
function cityrp.item.RunCrashProtection()
	local query = mysql:Select("items")
	query:Select("item_id")
	query:Select("unique_id")
	query:Select("stack")
	query:Select("data")
	query:Where("inventory_id", 0) -- Items in the 'world' inventory
	query:WhereNull("deleted")
	query:WhereJsonNotNull("data", "crashProtect") -- Items marked for crash protection
	query:Callback(function(result)
		if result and #result > 0 then
			for _, itemData in ipairs(result) do -- Renamed 'item' to 'itemData' to avoid conflict
				itemData.tData = util.JSONToTable(itemData.data, nil, true) or nil

				---@type string|nil
				local sid64 = itemData.tData and itemData.tData["crashProtect"]

				logger:Info("Crash Protection attempting to recover item ", logger:Highlight(itemData.item_id),
					" from world inventory to player ", logger:Highlight(sid64), ".")

				if not istable(itemData.tData) or table.IsEmpty(itemData.tData) then
					logger:Critical("Crash Protection failed to recover item ", logger:Highlight(itemData.item_id),
						" from world inventory to player ", logger:Highlight(sid64),
						" due to malformed data (no data found).")
					continue
				end

				if not isstring(sid64) then
					logger:Critical("Crash Protection failed to recover item ", logger:Highlight(itemData.item_id),
						" from world inventory to player ", logger:Highlight(sid64),
						" due to malformed data (no valid sid64).")
					continue
				end

				local callback = function(invId)
					if not invId or not isnumber(invId) then
						logger:Critical("Crash Protection failed to recover item ", logger:Highlight(itemData.item_id),
							" from world inventory to player ", logger:Highlight(sid64),
							" due to invalid inventory.")
						return
					end

					itemData.tData["crashProtect"] = nil
					if itemData.tData["netOverride"] then itemData.tData["netOverride"] = nil end

					hook.Run("CrashProtectModifyData", itemData.tData)

					local dQuery = mysql:Update("items")
					dQuery:Update("inventory_id", invId)
					dQuery:Update("data", util.TableToJSON(itemData.tData))
					dQuery:Where("item_id", itemData.item_id)
					dQuery:Callback(function()
						logger:Info("Crash Protection recovered item ", logger:Highlight(itemData.item_id),
							" to player ", logger:Highlight(sid64), ".")
					end)
					dQuery:Execute()
				end

				cityrp.inventory.FindOfflinePlayerInventory(sid64, "bank", callback)
			end
		end
	end)
	query:Execute()
end

--- Call item death hooks when the player dies on the selected inventory
--- @param client Player # The player who died.
--- @param inv Inventory # The inventory to process hooks for.
function cityrp.item.ProcessDeathHooks(client, inv)
	if not inv or not IsValid(inv) then return end

	local items = inv:GetItemsSeq()

	for _, item in ipairs(items) do

		local base = item:GetBase()

		if base.PlayerDeathHook then
			base:PlayerDeathHook(item, client)
		end
	end
end

--- Hook callback for player death to process item death hooks.
hook.Add("DoPlayerDeath", "itemProcessDeathHooks", function(client)
	if CLIENT then return end
	cityrp.item.ProcessDeathHooks(client, client:GetInventory())
end)

--- Calls item spawn hooks for items in the specified inventory when a player spawns.
--- @param client Player # The player who spawned.
--- @param inv Inventory # The inventory to process hooks for.
function cityrp.item.ProcessSpawnHooks(client, inv)
	if not inv or not IsValid(inv) then return end

	local items = inv:GetItemsSeq()

	for _, item in ipairs(items) do

		local base = item:GetBase()

		if base.PlayerSpawnHook then
			base:PlayerSpawnHook(item, client)
		end
	end
end

--- Hook callback for player spawn to process item spawn hooks.
hook.Add("PlayerSpawn", "itemProcessSpawnHooks", function(client)
	cityrp.item.ProcessSpawnHooks(client, client:GetInventory())
end)
