// bmw_x5_48i, created by [FL] Conn in Sat Jun 25 13:53:57 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 -4 1"
		"MassOverride"			"5800"
		"AddGravity"			"0.5"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"420"
		"MaxRPM"				"4000"
		"MaxSpeed"				"70"
		"MaxReverseSpeed"		"24"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"6"
		"Gear"					"5"
		"Gear"					"2.4"
		"Gear"					"1.5"
		"Gear"					"1"
		"Gear"					"0.84"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"8"
		"DegreesBoost"						"11"
		"FastDampen"						"70"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"5"
		"FastCarSpeed"						"55"
		"SlowSteeringRate"					"2.7"
		"FastSteeringRate"					"1.5"
		"SteeringRestRateSlow"				"2.7"
		"SteeringRestRateFast"				"1.5"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.5"
		"BrakeSteeringRateFactor"			"2"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.8"
			"Mass"							"250"
			"Inertia"						"0"
			"Damping"						"0"
			"RotDamping"					"0.4"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"90"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"20"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.4"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"17.8"
			"Mass"							"400"
			"Inertia"						"0"
			"Damping"						"0.9"
			"RotDamping"					"0.4"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"100"
			"SpringDamping"					"0.5"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"28"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.02"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.2"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.38"
		"Speed_Approach_Factor"	"0.052"
	}
	"Gear"
	{
		"Max_Speed"				"0.49"
		"Speed_Approach_Factor"	"0.034"
	}
	"Gear"
	{
		"Max_Speed"				"1.5"
		"Speed_Approach_Factor"	"0.033"
	}
	"Gear"
	{
		"Max_Speed"				"2"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/slowdown_highspeed.mp3"
		"Min_Time"	"2"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"common/null.mp3"
		"Min_Time"	"4"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/idle.mp3"
		"Min_Time"	"0.05"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/lwcars/bmw_x5_48i/slowdown_slow.mp3"
		"Min_Time"	"2"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
