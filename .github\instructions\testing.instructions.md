---
applyTo: "tests/**"
---

# GLuaTest Testing Instructions

Scope: Write and maintain unit/integration tests using GLuaTest for CityRP systems, items, plugins, and utilities.

When to use: Whenever adding features, fixing bugs, or refactoring behavior in core or plugins.

## Test Structure
Return a test group table with `groupName`, `cases`, and optional lifecycle hooks.

```lua
return {
    groupName = "Feature Tests",
    beforeEach = function(state)
        -- Setup code
    end,
    afterEach = function(state)
        -- Teardown / cleanup
    end,
    cases = {
        {
            name = "Test Description",
            timeout = 2000, -- optional ms
            func = function(state)
                expect(actual).to.equal(expected)
            end
        }
    }
}
```

## Expectations Reference
- Equality: `expect(x).to.equal(y)`, `expect(x).to.aboutEqual(y, tolerance)`
- Comparison: `expect(x).to.beLessThan(y)`, `expect(x).to.beGreaterThan(y)`
- Type: `expect(x).to.beA("string")`, `expect(x).to.beNil()`
- Boolean: `expect(x).to.beTrue()`, `expect(x).to.beFalse()`
- Validity: `expect(entity).to.beValid()`, `expect(entity).to.beInvalid()`
- Errors: `expect(func).to.err()`, `expect(func).to.succeed()`

## Stubs and Mocking
```lua
local stubFn = stub(table, "functionName").returns(value)
-- ... run code under test ...
expect(stubFn).was.called()
-- Restored automatically after test
```

## State Management
- Use `state` for sharing data between hooks and cases
- Store cleanup handles in `state` to ensure teardown
- Prefer deterministic fixtures; avoid global mutations

## Do / Don’t
Do:
- Test both success and failure paths
- Keep timeouts minimal for async tests
- Use descriptive, behavior-focused names
- Isolate side effects with setup/teardown

Don’t:
- Depend on networked state unless explicitly mocked
- Rely on cross-plugin behavior for a single plugin’s tests
- Modify core or plugin code in tests; use stubs/mocks instead
