---
applyTo: "tests/**"
---

# GLuaTest Testing Instructions

Scope: Write and maintain unit/integration tests using GLuaTest for CityRP systems, items, plugins, and utilities.

When to use: Whenever adding features, fixing bugs, or refactoring behavior in core or plugins.

## Test Structure
Return a test group table with `groupName`, `cases`, and optional lifecycle hooks.

```lua
return {
    groupName = "Feature Tests",
    beforeAll = function(state)
        -- One-time setup before all tests
    end,
    beforeEach = function(state)
        -- Setup code before each test
    end,
    afterEach = function(state)
        -- Teardown / cleanup after each test
    end,
    afterAll = function(state)
        -- One-time cleanup after all tests
    end,
    cases = {
        {
            name = "Test Description",
            async = false, -- Set to true for async tests
            timeout = 60, -- Timeout in seconds (default 60)
            func = function(state)
                expect(actual).to.equal(expected)
            end,
            cleanup = function(state)
                -- Optional per-test cleanup
            end
        }
    }
}
```

## Expectations Reference
- Equality: `expect(x).to.equal(y)`, `expect(x).to.aboutEqual(y, tolerance)`
- Comparison: `expect(x).to.beLessThan(y)`, `expect(x).to.beGreaterThan(y)`
- Type: `expect(x).to.beA("string")`, `expect(x).to.beNil()`
- Boolean: `expect(x).to.beTrue()`, `expect(x).to.beFalse()`
- Validity: `expect(entity).to.beValid()`, `expect(entity).to.beInvalid()`
- Errors: `expect(func).to.err()`, `expect(func).to.succeed()`

## Stubs and Mocking
```lua
-- Create a stub that returns a specific value
local stubFn = stub(table, "functionName").returns(value)

-- Create a stub with custom function
local stubFn = stub(table, "functionName").with(function(...) return customValue end)

-- Create a stub that returns values in sequence
local stubFn = stub(table, "functionName").returnsSequence({val1, val2}, defaultVal)

-- ... run code under test ...

-- Verify stub was called
expect(stubFn).was.called()
expect(stubFn).wasNot.called()

-- Stubs are automatically restored after each test
```

## State Management
- Use `state` for sharing data between hooks and cases
- Store cleanup handles in `state` to ensure teardown
- Prefer deterministic fixtures; avoid global mutations

## Async Tests
For tests that require timers or network operations:

```lua
{
    name = "Async Test Example",
    async = true,
    timeout = 2, -- Keep timeout as low as possible (in seconds)
    func = function(state)
        timer.Simple(1, function()
            expect(true).to.beTrue()
            done() -- Signal test completion
        end)
    end
}
```

Use `done()` to signal completion and `fail("message")` to manually fail async tests.

## Do / Don’t
Do:
- Test both success and failure paths
- Keep timeouts minimal for async tests
- Use descriptive, behavior-focused names
- Isolate side effects with setup/teardown

Don’t:
- Depend on networked state unless explicitly mocked
- Rely on cross-plugin behavior for a single plugin’s tests
- Modify core or plugin code in tests; use stubs/mocks instead
- Use external testing functions not provided by GLuaTest
- Forget to call `done()` in async tests

## Framework Status
**Note:** GLuaTest is not currently implemented in this codebase. These instructions are provided for future implementation. When implementing tests, ensure GLuaTest is properly installed and configured before writing test files.
