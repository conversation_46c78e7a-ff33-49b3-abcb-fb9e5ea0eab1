--- Equippable base item
-- This is intended to act as a generic base or template for equippable items (items players can equip and de-equip)
-- Unlike base_place, this is made using the new inventory feature and should be used as an example for any new bases.
-- This base isn't fully ready to be used as is, it is more intened as a "base for a base", for example clothing and weapons uses this as a base but adds more functionality. It isn't really possible to make this generic due to how many different equippable items there may be (some are SWEPs, some are clothing, some are accessories, some are weapon bases, etc...)
-- That being said, it is possible to use as is if you just fill in the OnEquip and OnUnEquip functions with whatever you want. Just don't touch anything marked as private
-- @copyright Fearless Gaming
-- <AUTHOR>
-- @item base_equip
-- @alias ITEM
-- @baseclass ITEM

DEATH_DROP_NEVER = 0
DEATH_DROP_EQUIPPED = 1
DEATH_DROP_ALWAYS = 2
DEATH_DROP_CUSTOM = 3
DEATH_DROP_CUSTOM_NEVER = 4
DEATH_DROP_CUSTOM_EQUIPPED = 5
DEATH_DROP_CUSTOM_ALWAYS = 6
DEATH_DROP_CUSTOM_1 = 7
DEATH_DROP_CUSTOM_2 = 8
---@alias DEATH_DROP
---|`DEATH_DROP_NEVER` # Will never drop this item on death (default)
---|`DEATH_DROP_EQUIPPED` # Will drop this item only if it is equipped on death
---|`DEATH_DROP_ALWAYS` # Will always drop this item, regardless of if it is equipped
---|`DEATH_DROP_CUSTOM` # Will call the OnPlayerDeath hook for custom behaviour

--- @class base_equip : base_item
--- @field SetEquipSlot fun(self: self, slotName: string): self Sets this item's equipment "slot". This is a unique string representing where this item goes. It assumes only one item can fit per "slot". If nil, the slot system will be ignored.
--- @field GetEquipSlot fun(self: self): boolean Gets this item's equipment "slot".
--- @field SetDropOnDeath fun(self: self, dropOnDeath: DEATH_DROP): self Sets if this item should drop on player death (default false)
--- @field GetDropOnDeath fun(self: self): DEATH_DROP Gets if this item should drop on player death
local ITEM = ITEM:New()
	:SetName("Base Equippable Item")
	:SetCost(0)
	:SetStore(0)
	:SetSize(0)
	:SetCategory("Other")
	:DisableAutoActions()

	--- Sets this item's equipment "slot"
	-- @string equipSlot The name of the slot
	-- @function ITEM:SetEquipSlot

	--- Gets this item's equipment "slot", this is a string that represents a unique slot
	-- @function ITEM:GetEquipSlot
	-- @rstring The equip slot, false if not set
	:AutoFunction("EquipSlot", "equipSlot")(false)

	--- Sets if this item should drop on death (default 0)
	-- @integer dropOnDeath Enum for the intended action (0 = never, )
	-- @function ITEM:SetDropOnDeath

	--- Gets if this item will drop on death
	-- @function ITEM:GetDropOnDeath
	-- @rbool dropOnDeath
	:AutoFunction("DropOnDeath", "dropOnDeath")(0)

---@private
function ITEM:ItemRegistered()
	if CLIENT then return end

	local deathBehaviour = self:GetDropOnDeath()

	if deathBehaviour == DEATH_DROP_EQUIPPED or deathBehaviour == DEATH_DROP_ALWAYS then
		self.PlayerDeathHook = self.HandleDeathDropDefault
	elseif deathBehaviour >= DEATH_DROP_CUSTOM then
		self.PlayerDeathHook = self.OnPlayerDeath
	end
end

function ITEM:ItemCreated(item)
	if SERVER then
		item:SetData("equipped", false)
	end
end

function ITEM:PlayerSpawnHook(item, client)
	if item:IsEquipped() then
		item:SetEquipped(false)
	end
end

---@param item Item
---@param state boolean
---@param ent? Entity
function ITEM:ToggleEquipState(item, state, ent)
	item:SetData("equipped", state)

	if ent and IsValid(ent) then
		item:SetEntity(ent)
	else
		item:SetEntity(nil)
	end
end

---@private
---@param item Item
---@param client Player
---@return boolean
function ITEM:EquipCondition(item, client)

	if item:IsEquipped() or not client:ValidState() then
		return false
	end

	if not self:CanPlayerEquip(item, client) then
		return false
	end

	return true
end

---@private
---@param item Item
---@param client Player
function ITEM:EquipAction(item, client)

	local ent = self:OnPlayerEquip(item, client)

	self:ToggleEquipState(item, true, ent)

end

---@private
---@param item Item
---@param client Player
---@return boolean
function ITEM:UnEquipCondition(item, client)

	if not item:IsEquipped() or not client:ValidState() then
		return false
	end

	if not self:CanPlayerUnEquip(item, client) then
		return false
	end

	return true
end

---@private
---@param item Item
---@param client Player
function ITEM:UnEquipAction(item, client)

	local value = self:OnPlayerUnEquip(item, client)

	self:ToggleEquipState(item, false)
end

---Returns if this item is currently "equipped"
---@param item Item
---@return boolean
function ITEM:IsEquipped(item)
	return (item:GetData("equipped", false) or item:GetEntity())
end

---@private
---@param item Item
---@param client Player
function ITEM:HandleDeathDropDefault(item, client)
	local deathBehaviour = self:GetDropOnDeath()

	if not item:GetData("equipped", false) and deathBehaviour == DEATH_DROP_EQUIPPED then return end

	item:SetData("equipped", false)
	item:DropEnt(1, client:GetPos(), client)
end

--- The functions below are intended to be implemented by any child bases, they are called by the default functions above.

--- Called when the player tries to equip this item, implement in child base/item
---@param item Item
---@param client Player
---@return boolean # If the item can be equipped
function ITEM:CanPlayerEquip(item, client)
	return true
end

--- Called when the player tries to un-equip this item, implement in child base/item
---@param item Item
---@param client Player
---@return boolean # If the item can be un-equipped
function ITEM:CanPlayerUnEquip(item, client)
	return true
end

--- Called when the player equips this item, implement in child base/item.
--- Return the entity created if this makes some sort of SWEP, it'll then automatically set the entity to the item and vice versa.
---@param item Item
---@param client Player
---@return Entity?
function ITEM:OnPlayerEquip(item, client)
	return
end

--- Called when the player un-equips this item, implement in child base/item
--- If an entity was passed when equipped, this won't automatically remove it from the player, you'll need to do that yourself. This is to let you save any important data and such before removing it.
---@param item Item
---@param client Player
function ITEM:OnPlayerUnEquip(item, client)
	return
end

--- Called when the player dies if DropOnDeath is enabled, implement in child base/item if custom death override is enabled
---@param item Item
---@param client Player
function ITEM:OnPlayerDeath(item, client)
	return false
end

--- Used to paint a small icon to show this item is currently equipped
---@param panel cityrp_invNew_Item
---@param w number
---@param h number
---@param item Item
function ITEM:PaintOver(panel, w, h, item)
	if item:IsEquipped() then
		surface.SetDrawColor(110, 255, 110, 100)
		surface.DrawRect(w - 14, h - 14, 8, 8)
	end
end

function ITEM:PopulateTooltip(item, tooltip)
	if (item:IsEquipped()) then
		local name = tooltip:GetRow("name")
		name:SetBackgroundColor(derma.GetColor("Success", tooltip))
	end
end

function ITEM:NotEquipped(item, client)
	if item:IsEquipped() or not client:ValidState() then
		return false
	end

	return true
end

-- Action builders
ITEM:AddAction("Equip")
	:SetName("Equip")
	:SetIcon("icon16/brick.png")
	:SetCondition(ITEM.EquipCondition)
	:SetAction(ITEM.EquipAction)
	:Build()

ITEM:AddAction("Unequip")
	:SetName("Take Off")
	:SetIcon("icon16/brick.png")
	:SetCondition(ITEM.UnEquipCondition)
	:SetAction(ITEM.UnEquipAction)
	:Build()

-- Register default actions
ITEM:AddAction("Give")
	:SetCondition(ITEM.NotEquipped)
	:Build()

ITEM:AddAction("Drop")
	:SetCondition(ITEM.NotEquipped)
	:Build()


ITEM:AutoRegister()

-- Player stuff, in here for now probably move later

---@class Player
local pMeta = FindMetaTable("Player")


---Get a player's equipped item
---@param slot? string Slot to search for, if not specified will return all items
---@return Item|table? # Returns the item if slot was specified, or the entire table. If slot was specified, it may be nil
function pMeta:GetEquipped(slot)
	self.equipped = self.equipped or {}

	if slot then
		return self.equipped[slot]
	end

	return self.equipped
end

---Set something as being equipped by a player, intended for equip item use
---@param item Item
---@param slot string Slot to set the equipped item to
function pMeta:SetEquipped(item, slot)
	self.equipped = self.equipped or {}

	self.equipped[slot] = item
end

--- Setting some item class functions, in here because it is really only related to this item so it doesn't make sense in the actual item metatable. There needs to be a better way to do this that doesn't involve polluting the item metatable file as it is already a mess.
---@class Item
local nItem = cityrp.meta.item

function nItem:CanEquip()
	return self:GetData("equipped", nil) != nil
end

function nItem:GetEquipSlot()
	return self.equipSlot or false
end

function nItem:SetEquipped(state)
	if self:CanEquip() then
		self:GetBase() --[[@as base_equip]]
			:ToggleEquipState(self, state)
	end
end

function nItem:IsEquipped()
	return self:GetBase() --[[@as base_equip]]
		:IsEquipped(self)
end
