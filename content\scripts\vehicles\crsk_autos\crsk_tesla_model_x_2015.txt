
"Vehicle"
{
 "WheelsPerAxle" "2"
 "Body"
 {
 "CounterTorqueFactor" "0.9"
 "MassCenterOverride" "0 -15 3"
 "MassOverride" "1500"
 "AddGravity" "1.25"
 "MaxAngularVelocity" "100"
 }
 "Engine"
 {
 "HorsePower" "320"
 "MaxRPM" "3000"
 "MaxSpeed" "135"
 "MaxReverseSpeed" "24"
 "AutobrakeSpeedGain" "1.1"
 "AutobrakeSpeedFactor" "4"
 "Autotransmission" "0"
 "AxleRatio" "5"
 "Gear" "3.2"
 "Gear" "2.4"
 "Gear" "1.5"
 "Gear" "1"
 "Gear" "0.84"

 "ShiftUpRPM" "3800"
 "ShiftDownRPM" "1600"
 }
 "Steering"
 {
 "DegreesSlow" "40"
 "DegreesFast" "5"
 "DegreesBoost" "11"
 "FastDampen" "80"
 "SteeringExponent" "0"
 "SlowCarSpeed" "5"
 "FastCarSpeed" "60"
 "SlowSteeringRate" "3"
 "FastSteeringRate" "2"
 "SteeringRestRateSlow" "3"
 "SteeringRestRateFast" "2"
 "TurnThrottleReduceSlow" "0.01"
 "TurnThrottleReduceFast" "0.6"
 "BrakeSteeringRateFactor" "1.8"
 "ThrottleSteeringRestRateFactor" "2"
 "BoostSteeringRestRateFactor" "1.7"
 "BoostSteeringRateFactor" "1.7"

 "PowerSlideAccel" "250"

 "SkidAllowed" "1"
 "DustCloud" "1"
 }
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18"
			"Mass"							"125"
			"Inertia"						"1.5"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"75"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7"
			"MaxBodyForce"					"28"
		}
		"TorqueFactor"						"0.68"
		"BrakeFactor"						"0.65"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18"
			"Mass"							"400"
			"Inertia"						"1.5"
			"Damping"						"0.1"
			"RotDamping"					"0.9"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"70"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"7"
			"MaxBodyForce"					"30"
		}
		"TorqueFactor"						"0.68"
		"BrakeFactor"						"0.65"
	}
}


"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.05"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.20"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.9"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.2"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"1.7"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/sgmcars/models/start.mp3"
		"Min_Time"	"0.9"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/sgmcars/models/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/sgmcars/models/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/sgmcars/models/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/sgmcars/models/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/sgmcars/models/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/sgmcars/models/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
