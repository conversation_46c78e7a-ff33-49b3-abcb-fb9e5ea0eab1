# CityRP Plugin Development Guide for Copilot

**Goal:** Create a new CityRP plugin or modify an existing one based on the user's request.

**Information Gathering:**
*   If creating a *new* plugin and the name or primary purpose/features are not specified by the user, ask for them.

**Requirements & Constraints:**

1.  **Plugin Isolation:**
    *   **Strictly** work ONLY within the target plugin's directory (`plugins/<plugin_name>/`).
    *   Do **NOT** modify core files (`gamemode/`) or other plugins unless explicitly instructed or using established `cityrp.*` APIs.
    *   Reference: [#file:../workflows/copilot-instructions.md](../copilot-instructions.md) Section 3.

2.  **Core API Usage:**
    *   **Prioritize** using existing `cityrp.*` APIs before writing new helper functions.
    *   Key APIs:
        *   Utilities: `cityrp.util` ([#file:../../gamemode/core/sh_util.lua](../../gamemode/core/sh_util.lua)) - **Check first!** Includes `cityrp.util.Include`.
        *   Networking: `cityrp.net` ([#file:../../gamemode/core/libraries/sh_net.lua](../../gamemode/core/libraries/sh_net.lua)) for entity state. Standard `net` library for other messages.
        *   Database: `mysql` wrapper ([#file:../../gamemode/core/libraries/thirdparty/sv_mysql.lua](../../gamemode/core/libraries/thirdparty/sv_mysql.lua)) for data queries. Migrations ([#file:../../gamemode/core/migrations/](../../gamemode/core/migrations/)) for schema changes - **Note:** Core migrations are reference only; plugin migrations go in the plugin's `migrations/` folder.
        *   Logging: `cityrp.logging` ([#file:../../gamemode/core/libraries/sh_logging.lua](../../gamemode/core/libraries/sh_logging.lua)). Use `self:Logger()` in `PLUGIN:` methods.
        *   Items: `cityrp.item` ([#file:../../gamemode/core/libraries/sh_item.lua](../../gamemode/core/libraries/sh_item.lua), [#file:../../gamemode/core/libraries/sh_itemcore.lua](../../gamemode/core/libraries/sh_itemcore.lua)). **Also consult the `Item` metatable ([#file:../../gamemode/core/metatables/sh_item.lua](../../gamemode/core/metatables/sh_item.lua)) for methods on item instances and the `BaseItem` metatable ([#file:../../gamemode/core/metatables/sh_base_item.lua](../../gamemode/core/metatables/sh_base_item.lua)) for defining new item bases.** **Note:** Core item definitions ([#file:../../gamemode/core/items/](../../gamemode/core/items/)) are reference only; plugin items go in the plugin's `items/` folder.
        *   Inventories: `cityrp.inventory` ([#file:../../gamemode/core/libraries/sh_inventory.lua](../../gamemode/core/libraries/sh_inventory.lua)). **Also consult the `Inventory` metatable ([#file:../../gamemode/core/metatables/sh_inventory.lua](../../gamemode/core/metatables/sh_inventory.lua)) for available methods on inventory instances.**
        *   Commands: `cityrp.commandNew` ([#file:../../gamemode/core/libraries/sh_command.lua](../../gamemode/core/libraries/sh_command.lua)).
    *   Reference: [#file:../workflows/copilot-instructions.md](../copilot-instructions.md) Sections 2 & 4.

3.  **Plugin Structure:**
    *   **Entry Point:** `plugins/<plugin_name>/sh_plugin.lua`. This file is the main entry point for your plugin.
    *   **Basic `sh_plugin.lua` Structure (for New Plugins):** The following is a basic template for a *new* plugin's `sh_plugin.lua`. Existing plugins will already have this file.
        ```lua
        PLUGIN.name = "Plugin Name"
        PLUGIN.author = "Plugin Author"
        PLUGIN.description = "Plugin Description"

        -- Initialize example runtime data storage
        PLUGIN.exampleData = {}

        -- Optional: Called after the plugin and its auto-included files are loaded.
        function PLUGIN:OnLoaded()
            self:Logger():Info("Plugin loaded!")
            -- Load saved data when the plugin loads
            self:LoadData()
        end

        -- Optional: Called periodically by the server to save plugin data.
        -- Inside this function, use self:SetData() to save your plugin's current runtime data.
        function PLUGIN:SaveData()
            -- Example: Save the current runtime data (which might include a counter)
            -- Ensure self.exampleData exists and is a table
            self.exampleData = self.exampleData or {}
            self.exampleData.someCounter = (self.exampleData.someCounter or 0) + 1 -- Example modification

            self:SetData(self.exampleData) -- Save the current runtime data
            self:Logger():Debug("Data saved.")
        end

        -- Optional: Called by the server after map load to load plugin data.
        -- Inside this function, use self:GetData() to load your plugin's data into a runtime variable.
        -- Also called manually in OnLoaded in this example for initial load.
        function PLUGIN:LoadData()
            -- Load saved data into the plugin's exampleData field, defaulting to an empty table
            self.exampleData = self:GetData({}) -- Use self:GetData() to retrieve saved data
            self:Logger():Info("Data loaded. Counter is: ", self.exampleData.someCounter or 0)
        end

        -- Example Hook: Called when a player initially spawns.
        function PLUGIN:PlayerInitialSpawn(ply)
            -- Add player spawn logic here
        end
        ```
        **Note:** This is a starting point. You will need to modify the `PLUGIN.name`, `PLUGIN.author`, `PLUGIN.description`, data handling (`exampleData`, `SaveData`, `LoadData`), and add/remove hooks (`PlayerInitialSpawn`, etc.) to fit the specific requirements of your plugin.
    *   **Standard Subdirectories & Loading Order:** The core plugin loader automatically includes files from specific subdirectories within your plugin folder (`plugins/<plugin_name>/`) in a predefined order. Create these folders as needed:
        1.  **`languages/`:** Contains language files (e.g., `en.lua`). Loaded first to ensure translations are available early.
        2.  **`libs/`:** For shared library code (helper functions, classes) used within the plugin. Loaded after languages.
        3.  **`items/`:** Place plugin-specific item definitions here. Use core item definitions ([#file:../../gamemode/core/items/](../../gamemode/core/items/)) as a reference. Loaded after libs.
        4.  **`plugins/`:** For nested plugins (plugins loaded by *this* plugin). Loaded after items.
        5.  **`derma/`:** Contains VGUI/Derma UI elements specific to the plugin. Loaded after nested plugins.
        6.  **`entities/`:** Contains custom entities, weapons, effects, or tools specific to the plugin. Loaded after derma.
        7.  **`itemspostload/`:** For code that needs to run *after* all items (core and plugin) have been loaded. Useful for modifying or interacting with items defined elsewhere. Loaded after entities.
        8.  **`migrations/`:** Place plugin-specific database migration files here (e.g., `sv_[timestamp]_add_myplugin_table.lua`). Use core migrations ([#file:../../gamemode/core/migrations/](../../gamemode/core/migrations/)) as a reference. Loaded last.
    *   **Auto-Includes Details:** See [#file:../../gamemode/core/libraries/sh_plugin.lua](../../gamemode/core/libraries/sh_plugin.lua) for the exact loading logic.

4.  **Coding Conventions:**
    *   **Plugin Scope:** Define plugin-specific functions and hooks using `PLUGIN:FunctionName()`. This automatically registers the function as a hook if `FunctionName` matches a standard Garry's Mod hook name (e.g., `PLUGIN:PlayerSpawn(ply)`).
    * All `PLUGIN:FunctionName()` and other PLUGIN functions are also hooks, as long as they are defined anywhere within the PLUGIN
    * Avoid using globals where possible. Use local functions instead of PLUGIN functions if it is important for that function to not be called outside of the current file.
    *   **Other Hooks:** Use standard `hook.Add("HookName", "UniqueKey", function(...) ... end)` primarily when:
        *   You need a unique key to prevent duplicate hooks.
        *   You want to split a large hook implementation into multiple, smaller functions within the same plugin.
        *   Hooking into a non-standard or custom hook.
    *   **Language:** Remember Lua is **case-sensitive** and **not strictly typed**. Verify all symbol names (functions, variables, table keys).
    *   **Style:** Match the style and patterns of existing code within the plugin or core libraries.

**Task:** Based on the user's request and the requirements above, generate the necessary code and file structure *within the specified plugin directory*. Ensure all code respects plugin isolation and utilizes core APIs correctly.
