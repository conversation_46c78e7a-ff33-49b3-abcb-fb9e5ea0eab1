// vw_multivan, created by [FL] Conn in Fri Jun 24 16:01:49 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.9"
		"MassCenterOverride"	"0 0 3"
		"MassOverride"			"2300"
		"AddGravity"			"0.65"
		"MaxAngularVelocity"	"720"
	}
	"Engine"
	{
		"HorsePower"			"240"
		"MaxRPM"				"4000"
		"MaxSpeed"				"55"
		"MaxReverseSpeed"		"24"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"4"
		"Autotransmission"		"0"
		"AxleRatio"				"5"
		"Gear"					"3"

		"ShiftUpRPM"			"3800"
		"ShiftDownRPM"			"1600"
	}
	"Steering"
	{
		"DegreesSlow"						"40"
		"DegreesFast"						"8"
		"DegreesBoost"						"11"
		"FastDampen"						"90"
		"SteeringExponent"					"0.7"
		"SlowCarSpeed"						"5"
		"FastCarSpeed"						"60"
		"SlowSteeringRate"					"2.7"
		"FastSteeringRate"					"2"
		"SteeringRestRateSlow"				"2.7"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.01"
		"TurnThrottleReduceFast"			"0.6"
		"BrakeSteeringRateFactor"			"2.7"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1.7"
		"BoostSteeringRateFactor"			"1.7"

		"PowerSlideAccel"					"250"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16.3"
			"Mass"							"220"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"85"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"17"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.6"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"16.3"
			"Mass"							"220"
			"Inertia"						"0"
			"Damping"						"0.5"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"slidingrubbertire"
			"BrakeMaterial"					"brakingrubbertire"
		}
		"Suspension"
		{
			"SpringConstant"				"85"
			"SpringDamping"					"1"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"5"
			"MaxBodyForce"					"17"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.6"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.06"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.21"
		"Speed_Approach_Factor"	"0.08"
	}
	"Gear"
	{
		"Max_Speed"				"0.42"
		"Speed_Approach_Factor"	"0.05"
	}
	"Gear"
	{
		"Max_Speed"				"0.7"
		"Speed_Approach_Factor"	"0.035"
	}
	"Gear"
	{
		"Max_Speed"				"1"
		"Speed_Approach_Factor"	"0.01"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles\metrohd\vw_multivan\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles\metrohd\vw_multivan\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles\metrohd\vw_multivan\rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles\metrohd\vw_multivan\idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles\metrohd\vw_multivan\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles\metrohd\vw_multivan\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles\metrohd\vw_multivan\first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles\metrohd\vw_multivan\throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles\metrohd\vw_multivan\enginestart.mp3"
		"Min_Time"	"1"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles\metrohd\vw_multivan\fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles\metrohd\vw_multivan\third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles\metrohd\vw_multivan\first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles\metrohd\vw_multivan\idle.mp3"
		"Min_Time"	"0.08"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles\metrohd\vw_multivan\second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles\metrohd\vw_multivan\idle.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"tdmcars_skid"
	"Skid_NormalFriction"	"tdmcars_skid"
	"Skid_HighFriction"		"tdmcars_skid"
}
