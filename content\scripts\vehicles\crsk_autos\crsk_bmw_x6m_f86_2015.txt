// crsk_bmw_x6m_f86_2015, created by [FL] Conn in Sat Jun 25 13:55:05 2022, using Vehicle Controller (VCMod).

"Vehicle"
{
	"WheelsPerAxle"		"2"
	"Body"
	{
		"CounterTorqueFactor"	"0.95"
		"MassCenterOverride"	"0 0 5"
		"MassOverride"			"2904"
		"AddGravity"			"1.66"
		"MaxAngularVelocity"	"100"
	}
	"Engine"
	{
		"HorsePower"			"310"
		"MaxRPM"				"5000"
		"MaxSpeed"				"70"
		"MaxReverseSpeed"		"23"
		"AutobrakeSpeedGain"	"1.1"
		"AutobrakeSpeedFactor"	"3"
		"Autotransmission"		"0"
		"AxleRatio"				"4"
		"Gear"					"4.17"
		"Gear"					"2.34"
		"Gear"					"1.52"
		"Gear"					"1.14"
		"Gear"					"3.4"

		"ShiftUpRPM"			"5000"
		"ShiftDownRPM"			"4500"
	}
	"Steering"
	{
		"DegreesSlow"						"35"
		"DegreesFast"						"15"
		"DegreesBoost"						"5"
		"FastDampen"						"0"
		"SteeringExponent"					"0"
		"SlowCarSpeed"						"25"
		"FastCarSpeed"						"40"
		"SlowSteeringRate"					"2"
		"FastSteeringRate"					"1"
		"SteeringRestRateSlow"				"3"
		"SteeringRestRateFast"				"2"
		"TurnThrottleReduceSlow"			"0.5"
		"TurnThrottleReduceFast"			"1"
		"BrakeSteeringRateFactor"			"3"
		"ThrottleSteeringRestRateFactor"	"2"
		"BoostSteeringRestRateFactor"		"1"
		"BoostSteeringRateFactor"			"1"

		"PowerSlideAccel"					"200"

		"SkidAllowed"						"1"
		"DustCloud"							"1"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18.3"
			"Mass"							"850"
			"Inertia"						"0"
			"Damping"						"0"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"70"
			"SpringDamping"					"0.7"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"1.5"
		"BrakeFactor"						"0.5"
	}
	"Axle"
	{
		"Wheel"
		{
			"Radius"						"18.3"
			"Mass"							"850"
			"Inertia"						"0"
			"Damping"						"0.1"
			"RotDamping"					"0"
			"Material"						"phx_rubbertire2"
			"SkidMaterial"					"rubbertire"
			"BrakeMaterial"					"jeeptire"
		}
		"Suspension"
		{
			"SpringConstant"				"90"
			"SpringDamping"					"0.7"
			"StabilizerConstant"			"0"
			"SpringDampingCompression"		"4.5"
			"MaxBodyForce"					"100"
		}
		"TorqueFactor"						"0.5"
		"BrakeFactor"						"0.5"
	}
}

"Vehicle_Sounds"
{
	"Gear"
	{
		"Max_Speed"				"0.1"
		"Speed_Approach_Factor"	"1"
	}
	"Gear"
	{
		"Max_Speed"				"0.3"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.4"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.5"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.7"
		"Speed_Approach_Factor"	"0.07"
	}
	"Gear"
	{
		"Max_Speed"				"0.9"
		"Speed_Approach_Factor"	"0.03"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN_WATER"
		"Sound"		"atv_stall_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_2_RESUME"
		"Sound"		"vehicles/marcuscars/x5m/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_START_WATER"
		"Sound"		"atv_start_in_water"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_3_RESUME"
		"Sound"		"vehicles/marcuscars/x5m/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_REVERSE"
		"Sound"		"vehicles/marcuscars/x5m/rev.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_IDLE"
		"Sound"		"vehicles/marcuscars/x5m/idle.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_GEAR_1_RESUME"
		"Sound"		"vehicles/marcuscars/x5m/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4"
		"Sound"		"vehicles/marcuscars/x5m/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_1"
		"Sound"		"vehicles/marcuscars/x5m/first.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN_HIGHSPEED"
		"Sound"		"vehicles/marcuscars/x5m/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_TURBO"
		"Sound"		"vehicles/marcuscars/x5m/fourth_cruise.mp3"
		"Min_Time"	"2.5"
	}
	"State"
	{
		"Name"		"SS_SHUTDOWN"
		"Sound"		"vehicles/marcuscars/x5m/stop.mp3"
		"Min_Time"	"0"
	}
	"State"
	{
		"Name"		"SS_START_IDLE"
		"Sound"		"vehicles/marcuscars/x5m/start.mp3"
		"Min_Time"	"1.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_4_RESUME"
		"Sound"		"vehicles/marcuscars/x5m/fourth_cruise.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_3"
		"Sound"		"vehicles/marcuscars/x5m/third.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_GEAR_0_RESUME"
		"Sound"		"vehicles/marcuscars/x5m/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_0"
		"Sound"		"vehicles/marcuscars/x5m/first.mp3"
		"Min_Time"	"0.75"
	}
	"State"
	{
		"Name"		"SS_GEAR_2"
		"Sound"		"vehicles/marcuscars/x5m/second.mp3"
		"Min_Time"	"0.5"
	}
	"State"
	{
		"Name"		"SS_SLOWDOWN"
		"Sound"		"vehicles/marcuscars/x5m/throttle_off.mp3"
		"Min_Time"	"0"
	}
	"CrashSound"
	{
		"Min_Speed"			"350"
		"Min_Speed_Change"	"250"
		"Sound"				"atv_impact_medium"
		"Gear_Limit"		"1"
	}
	"CrashSound"
	{
		"Min_Speed"			"450"
		"Min_Speed_Change"	"350"
		"Sound"				"atv_impact_heavy"
		"Gear_Limit"		"0"
	}

	"Skid_LowFriction"		"common/null.mp3"
	"Skid_NormalFriction"	"common/null.mp3"
	"Skid_HighFriction"		"common/null.mp3"
}
